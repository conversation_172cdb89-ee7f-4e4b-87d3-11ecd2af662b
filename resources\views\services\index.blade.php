@extends('layouts.app')

@section('title', 'บริการของเรา - บริการงานศพครบวงจร')

@php
use Illuminate\Support\Facades\Storage;
@endphp

@section('content')
<!-- Hero Section -->
<section class="hero-section text-white parallax-bg">
    <!-- Particle Background -->
    <div class="particles-bg" id="particles-bg-services"></div>
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold mb-4">
                    <i class="fas fa-hands-helping me-3" style="color: var(--memorial-gold);"></i>บริการของเรา
                </h1>
                <p class="lead mb-4">ให้การดูแลและเอาใจใส่ในทุกรายละเอียด ด้วยความเคารพและเกียรติ</p>
                <a href="#services" class="btn btn-light btn-lg px-4">
                    <i class="fas fa-arrow-down me-2"></i>ดูบริการทั้งหมด
                </a>
            </div>
            <div class="col-lg-6 text-center">
                <i class="fas fa-hands-helping fa-10x opacity-75 floating-animation" style="color: var(--memorial-gold);"></i>
            </div>
        </div>
    </div>
</section>

<!-- Services Section -->
<section id="services" class="py-5">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="section-title">บริการของเรา</h2>
            <p class="text-muted lead">บริการครบวงจรด้วยความเคารพและเอาใจใส่ในทุกรายละเอียด</p>
        </div>

        @if($services && $services->count() > 0)
            <div class="row g-4">
                @foreach($services as $service)
                <div class="col-lg-4 col-md-6">
                    <div class="card memorial-card h-100 shadow-sm">
                        @php
                            // ตรวจสอบไฟล์ที่มีอยู่จริง
                            $imagePath = $service->image ? 'services/' . $service->image : null;
                            $imageExists = $imagePath && Storage::disk('public')->exists($imagePath);

                            // ถ้าไฟล์ไม่มี ลองหาไฟล์ที่มีชื่อคล้ายกัน
                            if (!$imageExists && $service->image) {
                                $files = Storage::disk('public')->files('services');
                                $baseName = pathinfo($service->image, PATHINFO_FILENAME);

                                foreach ($files as $file) {
                                    $fileName = basename($file);
                                    if (strpos($fileName, $baseName) !== false) {
                                        $imagePath = $file;
                                        $imageExists = true;
                                        break;
                                    }
                                }
                            }

                            $imageUrl = $imageExists ? url('storage/' . $imagePath) : asset('images/no-image.svg');
                        @endphp
                        <img src="{{ $imageUrl }}"
                             class="card-img-top"
                             alt="{{ $service->title }}"
                             style="height: 250px; object-fit: cover;"
                             onerror="this.src='{{ asset('images/no-image.svg') }}'; this.onerror=null;">
                        <div class="card-body d-flex flex-column">
                            <h5 class="card-title fw-bold text-primary">{{ $service->title }}</h5>
                            <p class="card-text text-muted flex-grow-1">{{ Str::limit($service->description, 120) }}</p>
                            <div class="d-flex justify-content-between align-items-center mt-3">
                                <span class="h4 text-success fw-bold mb-0">
                                    <i class="fas fa-tag me-1"></i>฿{{ number_format($service->price) }}
                                </span>
                                <a href="{{ route('services.show', $service) }}" class="btn btn-primary">
                                    <i class="fas fa-eye me-1"></i>ดูรายละเอียด
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-hands-helping fa-5x text-muted mb-4"></i>
                <h3 class="text-muted">ยังไม่มีบริการ</h3>
                <p class="text-muted">เรากำลังเตรียมบริการใหม่ให้คุณ</p>
            </div>
        @endif
    </div>
</section>

<!-- Memorial Divider -->
<div class="memorial-divider"></div>

<!-- CTA Section -->
<section class="section-elegant py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h3 class="fw-bold mb-3">ต้องการคำปรึกษาเพิ่มเติม?</h3>
                <p class="lead mb-0">ทีมงานของเราพร้อมให้คำปรึกษาและดูแลทุกรายละเอียด ด้วยความเข้าใจและเอาใจใส่</p>
            </div>
            <div class="col-lg-4 text-lg-end">
                <div class="d-flex gap-2 justify-content-lg-end">
                    <a href="/contact" class="btn btn-memorial btn-lg">
                        <i class="fas fa-phone me-2"></i>ติดต่อเรา
                    </a>
                    <a href="/packages" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-box-heart me-2"></i>แพ็กเกจ
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection 