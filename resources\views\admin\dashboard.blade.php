@extends('layouts.admin')

@section('title', 'แดชบอร์ด - Admin Panel')

@section('content')

<div class="container-fluid">
    <!-- Page Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">
                        <i class="fas fa-tachometer-alt me-2 text-primary"></i>แดชบอร์ด
                    </h1>
                    <p class="text-muted">ภาพรวมระบบจัดการเว็บไซต์ SoloShop</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item active">
                            <i class="fas fa-home"></i> แดชบอร์ด
                        </li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <!-- Statistics Cards -->
            <div class="row mb-4">
                <!-- Services Card -->
                <div class="col-lg-3 col-md-6 col-sm-6 col-12 mb-3">
                    <div class="card bg-gradient-info shadow-sm border-0 h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="text-white mb-0 font-weight-bold">{{ \App\Helpers\DashboardHelper::formatNumber($stats['services']['total']) }}</h3>
                                    <p class="text-white-50 mb-0">บริการทั้งหมด</p>
                                </div>
                                <div class="text-white-50">
                                    <i class="fas fa-tools fa-2x"></i>
                                </div>
                            </div>
                            <div class="mt-3">
                                <a href="{{ route('admin.services.index') }}" class="btn btn-light btn-sm">
                                    <i class="fas fa-arrow-right"></i> จัดการบริการ
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Packages Card -->
                <div class="col-lg-3 col-md-6 col-sm-6 col-12 mb-3">
                    <div class="card bg-gradient-success shadow-sm border-0 h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="text-white mb-0 font-weight-bold">{{ \App\Helpers\DashboardHelper::formatNumber($stats['packages']['total']) }}</h3>
                                    <p class="text-white-50 mb-0">แพ็กเกจทั้งหมด</p>
                                </div>
                                <div class="text-white-50">
                                    <i class="fas fa-box fa-2x"></i>
                                </div>
                            </div>
                            <div class="mt-3">
                                <a href="{{ route('admin.packages.index') }}" class="btn btn-light btn-sm">
                                    <i class="fas fa-arrow-right"></i> จัดการแพ็กเกจ
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Activities Card -->
                <div class="col-lg-3 col-md-6 col-sm-6 col-12 mb-3">
                    <div class="card bg-gradient-warning shadow-sm border-0 h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="text-white mb-0 font-weight-bold">{{ \App\Helpers\DashboardHelper::formatNumber($stats['activities']['total']) }}</h3>
                                    <p class="text-white-50 mb-0">กิจกรรมทั้งหมด</p>
                                </div>
                                <div class="text-white-50">
                                    <i class="fas fa-images fa-2x"></i>
                                </div>
                            </div>
                            <div class="mt-3">
                                <a href="{{ route('admin.activities.index') }}" class="btn btn-light btn-sm">
                                    <i class="fas fa-arrow-right"></i> จัดการกิจกรรม
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contacts Card -->
                <div class="col-lg-3 col-md-6 col-sm-6 col-12 mb-3">
                    <div class="card bg-gradient-danger shadow-sm border-0 h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="text-white mb-0 font-weight-bold">{{ \App\Helpers\DashboardHelper::formatNumber($stats['contacts']['unread']) }}</h3>
                                    <p class="text-white-50 mb-0">ข้อความใหม่</p>
                                    <small class="text-white-50">({{ \App\Helpers\DashboardHelper::formatNumber($stats['contacts']['total']) }} ทั้งหมด)</small>
                                </div>
                                <div class="text-white-50">
                                    <i class="fas fa-envelope fa-2x"></i>
                                </div>
                            </div>
                            <div class="mt-3">
                                <a href="{{ route('admin.contacts.index') }}" class="btn btn-light btn-sm">
                                    <i class="fas fa-arrow-right"></i> ดูข้อความ
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card shadow-sm border-0">
                        <div class="card-header bg-white border-0">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-bolt text-primary"></i> การดำเนินการด่วน
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-2 col-md-4 col-sm-6 col-12 mb-3">
                                    <a href="{{ route('admin.services.create') }}" class="btn btn-outline-primary btn-block h-100 d-flex flex-column justify-content-center align-items-center py-3">
                                        <i class="fas fa-plus-circle fa-2x mb-2"></i>
                                        <span>เพิ่มบริการ</span>
                                    </a>
                                </div>
                                <div class="col-lg-2 col-md-4 col-sm-6 col-12 mb-3">
                                    <a href="{{ route('admin.packages.create') }}" class="btn btn-outline-success btn-block h-100 d-flex flex-column justify-content-center align-items-center py-3">
                                        <i class="fas fa-plus-circle fa-2x mb-2"></i>
                                        <span>เพิ่มแพ็กเกจ</span>
                                    </a>
                                </div>
                                <div class="col-lg-2 col-md-4 col-sm-6 col-12 mb-3">
                                    <a href="{{ route('admin.activities.create') }}" class="btn btn-outline-warning btn-block h-100 d-flex flex-column justify-content-center align-items-center py-3">
                                        <i class="fas fa-plus-circle fa-2x mb-2"></i>
                                        <span>เพิ่มกิจกรรม</span>
                                    </a>
                                </div>
                                <div class="col-lg-2 col-md-4 col-sm-6 col-12 mb-3">
                                    <a href="{{ route('admin.activity-categories.create') }}" class="btn btn-outline-info btn-block h-100 d-flex flex-column justify-content-center align-items-center py-3">
                                        <i class="fas fa-plus-circle fa-2x mb-2"></i>
                                        <span>เพิ่มหมวดหมู่</span>
                                    </a>
                                </div>
                                <div class="col-lg-2 col-md-4 col-sm-6 col-12 mb-3">
                                    <a href="{{ route('admin.homepage.index') }}" class="btn btn-outline-secondary btn-block h-100 d-flex flex-column justify-content-center align-items-center py-3">
                                        <i class="fas fa-edit fa-2x mb-2"></i>
                                        <span>แก้ไขหน้าแรก</span>
                                    </a>
                                </div>
                                <div class="col-lg-2 col-md-4 col-sm-6 col-12 mb-3">
                                    <a href="{{ route('home') }}" target="_blank" class="btn btn-outline-dark btn-block h-100 d-flex flex-column justify-content-center align-items-center py-3">
                                        <i class="fas fa-external-link-alt fa-2x mb-2"></i>
                                        <span>ดูเว็บไซต์</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activities -->
            <div class="row">
                <div class="col-lg-6 col-12 mb-4">
                    <div class="card shadow-sm border-0">
                        <div class="card-header bg-white border-0">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-clock text-info"></i> บริการล่าสุด
                            </h5>
                        </div>
                        <div class="card-body">
                            @if(isset($stats['services']['recent']) && $stats['services']['recent']->count() > 0)
                                <div class="list-group list-group-flush">
                                    @foreach($stats['services']['recent'] as $service)
                                        <div class="list-group-item border-0 px-0">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <h6 class="mb-1">{{ $service->title }}</h6>
                                                    <p class="mb-1 text-muted small">{{ Str::limit($service->description, 50) }}</p>
                                                    <small class="text-muted">{{ $service->formatted_price }}</small>
                                                </div>
                                                <div>
                                                    <a href="{{ route('admin.services.edit', $service) }}" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            @else
                                <p class="text-muted text-center py-3">ยังไม่มีบริการ</p>
                            @endif
                        </div>
                    </div>
                </div>

                <div class="col-lg-6 col-12 mb-4">
                    <div class="card shadow-sm border-0">
                        <div class="card-header bg-white border-0">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-clock text-success"></i> แพ็กเกจล่าสุด
                            </h5>
                        </div>
                        <div class="card-body">
                            @if(isset($stats['packages']['recent']) && $stats['packages']['recent']->count() > 0)
                                <div class="list-group list-group-flush">
                                    @foreach($stats['packages']['recent'] as $package)
                                        <div class="list-group-item border-0 px-0">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <h6 class="mb-1">{{ $package->name }}</h6>
                                                    <p class="mb-1 text-muted small">{{ Str::limit($package->description, 50) }}</p>
                                                    <small class="text-muted">{{ $package->formatted_price }}</small>
                                                </div>
                                                <div>
                                                    <a href="{{ route('admin.packages.edit', $package) }}" class="btn btn-sm btn-outline-success">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            @else
                                <p class="text-muted text-center py-3">ยังไม่มีแพ็กเกจ</p>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

@push('styles')
<style>
.card {
    transition: transform 0.2s ease-in-out;
}
.card:hover {
    transform: translateY(-2px);
}
.btn:hover {
    transform: translateY(-1px);
}
.bg-gradient-info {
    background: linear-gradient(45deg, #17a2b8, #20c997);
}
.bg-gradient-success {
    background: linear-gradient(45deg, #28a745, #20c997);
}
.bg-gradient-warning {
    background: linear-gradient(45deg, #ffc107, #fd7e14);
}
.bg-gradient-danger {
    background: linear-gradient(45deg, #dc3545, #e83e8c);
}
</style>
@endpush

@endsection