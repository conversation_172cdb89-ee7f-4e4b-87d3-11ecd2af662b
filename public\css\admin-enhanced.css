/* Enhanced Admin UI Styles */

/* Dashboard Improvements */
.small-box {
    transition: all 0.3s ease;
    cursor: pointer;
}

.small-box:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,.2);
}

.small-box .inner h3 {
    font-size: 2.5rem;
    font-weight: 700;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

.small-box .inner p {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Quick Actions Enhancement */
.card-header h3 {
    font-weight: 600;
    color: #495057;
}

.btn-block {
    transition: all 0.3s ease;
    font-weight: 500;
    border-radius: 8px;
    padding: 12px 20px;
}

.btn-block:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* Table Enhancements */
.table {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.table thead th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: none;
    font-weight: 600;
    color: #495057;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
    padding: 15px 12px;
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
    transform: scale(1.01);
}

.table tbody td {
    padding: 15px 12px;
    vertical-align: middle;
    border-color: #e9ecef;
}

/* Image Thumbnails */
.img-thumbnail {
    border-radius: 8px;
    transition: all 0.3s ease;
    border: 2px solid #e9ecef;
}

.img-thumbnail:hover {
    transform: scale(1.05);
    border-color: #007bff;
    box-shadow: 0 4px 12px rgba(0,123,255,0.3);
}

/* Button Groups */
.btn-group .btn {
    border-radius: 6px;
    margin: 0 2px;
    transition: all 0.2s ease;
}

.btn-group .btn:hover {
    transform: translateY(-1px);
}

/* Search Input Enhancement */
.input-group {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-radius: 8px;
    overflow: hidden;
}

.input-group .form-control {
    border: none;
    padding: 12px 15px;
    font-size: 0.95rem;
}

.input-group .form-control:focus {
    box-shadow: none;
    border-color: transparent;
}

.input-group-append .btn {
    border: none;
    padding: 12px 15px;
    background: #f8f9fa;
    color: #6c757d;
    transition: all 0.2s ease;
}

.input-group-append .btn:hover {
    background: #e9ecef;
    color: #495057;
}

/* Filter Dropdown */
.dropdown-menu {
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    border: none;
    padding: 8px 0;
}

.dropdown-item {
    padding: 10px 20px;
    font-size: 0.9rem;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    transform: translateX(5px);
}

/* Bulk Actions */
.bulk-actions {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 15px 20px;
    border-radius: 8px;
    border: 1px solid #dee2e6;
    margin-bottom: 20px;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.bulk-actions .btn-group .btn {
    border-radius: 6px;
    font-weight: 500;
    padding: 8px 16px;
}

/* Loading States */
.btn.loading {
    position: relative;
    color: transparent !important;
}

.btn.loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced Modals */
.modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 10px 40px rgba(0,0,0,0.2);
}

.modal-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    border-radius: 12px 12px 0 0;
    padding: 20px 25px;
}

.modal-title {
    font-weight: 600;
    color: #495057;
}

.modal-body {
    padding: 25px;
}

.modal-footer {
    border-top: 1px solid #dee2e6;
    padding: 20px 25px;
    background: #f8f9fa;
    border-radius: 0 0 12px 12px;
}

/* Form Enhancements */
.form-group label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

.form-control {
    border-radius: 8px;
    border: 1px solid #ced4da;
    padding: 12px 15px;
    font-size: 0.95rem;
    transition: all 0.2s ease;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

/* Alert Enhancements */
.alert {
    border-radius: 8px;
    border: none;
    padding: 15px 20px;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

/* Info Boxes */
.info-box {
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.info-box:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

.info-box-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin-bottom: 15px;
    margin: 0 auto 15px;
}

.info-box-content {
    text-align: center;
}

.info-box-text {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-box-number {
    font-size: 2rem;
    font-weight: 700;
    color: #495057;
    display: block;
    margin: 5px 0;
}

.info-box small {
    color: #6c757d;
    font-size: 0.8rem;
}

/* List Group Enhancements */
.list-group-item {
    border-radius: 8px !important;
    margin-bottom: 8px;
    transition: all 0.2s ease;
}

.list-group-item:hover {
    background-color: #f8f9fa;
    transform: translateX(5px);
}

.list-group-flush .list-group-item {
    border-left: 3px solid transparent;
}

.list-group-flush .list-group-item:hover {
    border-left-color: #007bff;
}

/* Responsive Improvements */
@media (max-width: 768px) {
    .small-box .inner h3 {
        font-size: 2rem;
    }

    .info-box-number {
        font-size: 1.5rem;
    }

    .btn-group .btn {
        margin: 2px 0;
    }

    .card-tools {
        flex-direction: column;
        align-items: stretch;
    }

    .card-tools .input-group,
    .card-tools .btn-group,
    .card-tools .btn {
        margin: 5px 0;
        width: 100%;
    }
}
.input-group .form-control {
    border-radius: 8px 0 0 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.input-group .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.input-group-append .btn {
    border-radius: 0 8px 8px 0;
    border: 2px solid #e9ecef;
    border-left: none;
}

/* Card Enhancements */
.card {
    border-radius: 12px;
    border: none;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 4px 20px rgba(0,0,0,0.12);
}

.card-header {
    border-radius: 12px 12px 0 0;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-bottom: 1px solid #e9ecef;
}

/* Badge Enhancements */
.badge {
    font-size: 0.8rem;
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: 500;
}

.badge-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.badge-danger {
    background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
}

.badge-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    color: #212529;
}

/* Alert Enhancements */
.alert {
    border-radius: 10px;
    border: none;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    font-weight: 500;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

/* Loading States */
.loading {
    position: relative;
    pointer-events: none;
    opacity: 0.6;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Improvements */
@media (max-width: 768px) {
    .small-box .inner h3 {
        font-size: 2rem;
    }
    
    .btn-block {
        padding: 10px 15px;
        font-size: 0.9rem;
    }
    
    .table-responsive {
        border-radius: 8px;
    }
    
    .card {
        margin-bottom: 1rem;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Success/Error States */
.success-state {
    border-left: 4px solid #28a745;
    background-color: #f8fff9;
}

.error-state {
    border-left: 4px solid #dc3545;
    background-color: #fff8f8;
}

/* Quick Action Section Styling */
.quick-actions-section h5 {
    font-weight: 600;
    color: #6c757d;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 8px;
    margin-bottom: 15px;
}

/* Enhanced Breadcrumb */
.breadcrumb {
    background: transparent;
    padding: 0;
    margin: 0;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "›";
    color: #6c757d;
    font-weight: bold;
}

.breadcrumb-item a {
    color: #007bff;
    text-decoration: none;
    transition: color 0.2s ease;
}

.breadcrumb-item a:hover {
    color: #0056b3;
}
