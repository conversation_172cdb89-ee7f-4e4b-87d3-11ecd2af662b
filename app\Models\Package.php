<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Package extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'price',
        'image',
    ];

    protected $casts = [
        'price' => 'decimal:2',
    ];

    /**
     * Get the image URL attribute
     */
    public function getImageUrlAttribute()
    {
        return \App\Helpers\ImageHelper::getImageUrl($this->image ? 'packages/' . $this->image : null);
    }

    /**
     * Get formatted price
     */
    public function getFormattedPriceAttribute()
    {
        return '฿' . number_format($this->price, 0);
    }
}
