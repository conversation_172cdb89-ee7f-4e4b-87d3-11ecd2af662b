@extends('layouts.admin')

@section('title', 'แก้ไขแพ็กเกจ - Admin Panel')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">
                        <i class="fas fa-edit me-2 text-success"></i>แก้ไขแพ็กเกจ
                    </h1>
                    <p class="text-muted">แก้ไขข้อมูลแพ็กเกจ: {{ $package->name }}</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item">
                            <a href="{{ route('admin.dashboard') }}">
                                <i class="fas fa-home"></i> แดชบอร์ด
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="{{ route('admin.packages.index') }}">
                                <i class="fas fa-box"></i> จัดการแพ็กเกจ
                            </a>
                        </li>
                        <li class="breadcrumb-item active">
                            <i class="fas fa-edit"></i> แก้ไขแพ็กเกจ
                        </li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row justify-content-center">
                <div class="col-lg-8 col-12">
                    <div class="card shadow-sm border-0">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-edit"></i> แก้ไขแพ็กเกจ: {{ $package->name }}
                            </h5>
                        </div>
                        
                        <form action="{{ route('admin.packages.update', $package) }}" method="POST" enctype="multipart/form-data" id="packageForm">
                            @csrf
                            @method('PUT')
                            <input type="hidden" name="remove_image" id="removeImageFlag" value="0">

                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            <label for="name" class="form-label">ชื่อแพ็กเกจ <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                                   id="name" name="name" value="{{ old('name', $package->name) }}" required>
                                            @error('name')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="description" class="form-label">รายละเอียดแพ็กเกจ <span class="text-danger">*</span></label>
                                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                                      id="description" name="description" rows="5" required>{{ old('description', $package->description) }}</textarea>
                                            @error('description')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="price" class="form-label">ราคา (บาท) <span class="text-danger">*</span></label>
                                            <div class="input-group">
                                                <span class="input-group-text">฿</span>
                                                <input type="number" class="form-control @error('price') is-invalid @enderror" 
                                                       id="price" name="price" value="{{ old('price', $package->price) }}" 
                                                       min="0" step="0.01" required>
                                                @error('price')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="image" class="form-label">รูปภาพแพ็กเกจ</label>
                                            <input type="file" class="form-control @error('image') is-invalid @enderror"
                                                   id="image" name="image" accept="image/*">
                                            <small class="form-text text-muted">รองรับไฟล์: JPG, PNG, GIF (ขนาดไม่เกิน 2MB)</small>
                                            @error('image')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        @php
                                            // ตรวจสอบว่ามีรูปภาพจริงๆ หรือไม่
                                            $hasImage = false;
                                            $imageUrl = asset('images/no-image.svg');
                                            $actualImagePath = null;

                                            if ($package->image) {
                                                $imagePath = 'packages/' . $package->image;
                                                $imageExists = \Illuminate\Support\Facades\Storage::disk('public')->exists($imagePath);

                                                // ถ้าไฟล์ไม่มี ลองหาไฟล์ที่มีชื่อคล้ายกัน
                                                if (!$imageExists) {
                                                    $files = \Illuminate\Support\Facades\Storage::disk('public')->files('packages');
                                                    $baseName = pathinfo($package->image, PATHINFO_FILENAME);

                                                    foreach ($files as $file) {
                                                        $fileName = basename($file);
                                                        if (strpos($fileName, $baseName) !== false) {
                                                            $imagePath = $file;
                                                            $imageExists = true;
                                                            break;
                                                        }
                                                    }
                                                }

                                                if ($imageExists) {
                                                    $hasImage = true;
                                                    $imageUrl = url('storage/' . $imagePath);
                                                    $actualImagePath = $imagePath;
                                                }
                                            }
                                        @endphp

                                        <div class="text-center">
                                            <label class="form-label">รูปภาพปัจจุบัน</label>

                                            @if($hasImage)
                                                <div id="imagePreview" style="display: block;">
                                                    <img id="previewImg" src="{{ $imageUrl }}"
                                                         class="img-thumbnail" style="max-width: 100%; max-height: 200px;"
                                                         alt="รูปภาพแพ็กเกจ"
                                                         onerror="this.src='{{ asset('images/no-image.svg') }}'; this.onerror=null;">
                                                    <div class="mt-2">
                                                        <button type="button" class="btn btn-sm btn-danger" id="removeImage">
                                                            <i class="fas fa-trash"></i> ลบรูปภาพ
                                                        </button>
                                                    </div>
                                                </div>
                                            @else
                                                <div id="imagePreview" style="display: none;">
                                                    <img id="previewImg" src="{{ asset('images/no-image.svg') }}"
                                                         class="img-thumbnail" style="max-width: 100%; max-height: 200px;"
                                                         alt="ไม่มีรูปภาพ">
                                                    <div class="mt-2">
                                                        <button type="button" class="btn btn-sm btn-danger" id="removeImage">
                                                            <i class="fas fa-trash"></i> ลบรูปภาพ
                                                        </button>
                                                    </div>
                                                </div>

                                                <div id="noImageDisplay" class="border border-dashed rounded p-4 text-muted">
                                                    <i class="fas fa-image fa-3x mb-2"></i>
                                                    <p>ไม่มีรูปภาพ</p>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="card-footer bg-light">
                                <div class="d-flex justify-content-between">
                                    <a href="{{ route('admin.packages.index') }}" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left"></i> กลับ
                                    </a>
                                    <div>
                                        <a href="{{ route('packages.show', $package) }}" target="_blank" class="btn btn-info me-2">
                                            <i class="fas fa-eye"></i> ดูหน้าบ้าน
                                        </a>
                                        <button type="submit" class="btn btn-warning">
                                            <i class="fas fa-save"></i> อัปเดตแพ็กเกจ
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    
                    <!-- Delete Section -->
                    <div class="card shadow-sm border-0 mt-3">
                        <div class="card-header bg-danger text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-trash"></i> ลบแพ็กเกจ
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="text-muted">การลบแพ็กเกจนี้จะไม่สามารถกู้คืนได้ กรุณาตรวจสอบให้แน่ใจก่อนดำเนินการ</p>
                            <button type="button" class="btn btn-danger" id="deletePackageBtn">
                                <i class="fas fa-trash"></i> ลบแพ็กเกจนี้
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

@push('styles')
<style>
.card {
    transition: transform 0.2s ease-in-out;
}
.form-control:focus {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40,167,69,.25);
}
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
$(document).ready(function() {
    // ตรวจสอบว่ามีรูปจริงๆ หรือไม่
    const hasImage = {{ $hasImage ? 'true' : 'false' }};
    const originalImageUrl = "{{ $imageUrl }}";
    const originalImageName = "{{ $package->image }}";

    // Debug: แสดง URL ใน console
    console.log('Has Image:', hasImage);
    console.log('Original Image URL:', originalImageUrl);
    console.log('Original Image Name:', originalImageName);

    // แสดงชื่อไฟล์ปัจจุบันในช่องเลือกไฟล์ (เฉพาะเมื่อมีรูปจริงๆ)
    if (hasImage && originalImageName) {
        // สร้าง label แสดงชื่อไฟล์ปัจจุบัน
        $('#image').after(`
            <div class="mt-1" id="currentFileName">
                <small class="text-primary">
                    <i class="fas fa-file-image"></i> ไฟล์ปัจจุบัน: <strong>${originalImageName}</strong>
                </small>
            </div>
        `);
    } else {
        // ถ้าไม่มีรูป ให้สร้าง div เปล่าไว้สำหรับแสดงข้อความ
        $('#image').after(`
            <div class="mt-1" id="currentFileName">
            </div>
        `);
    }

    // แสดงข้อความแจ้งเตือนเมื่ออัปเดทสำเร็จ
    @if(session('success'))
        Swal.fire({
            icon: 'success',
            title: 'สำเร็จ!',
            text: '{{ session('success') }}',
            timer: 3000,
            showConfirmButton: false
        });
    @endif

    // Image preview
    $('#image').on('change', function() {
        let file = this.files[0];
        if (file) {
            // ยกเลิกการลบรูปภาพ (ถ้ามี)
            $('#removeImageFlag').val('0');

            let reader = new FileReader();
            reader.onload = function(e) {
                $('#previewImg').attr('src', e.target.result);
                $('#imagePreview').show();
                $('#noImageDisplay').hide();

                // อัปเดทชื่อไฟล์ที่แสดง
                $('#currentFileName').html(`
                    <small class="text-success">
                        <i class="fas fa-file-image"></i> ไฟล์ใหม่: <strong>${file.name}</strong>
                    </small>
                `);

                // เปลี่ยนปุ่มกลับเป็นลบรูปภาพ
                $('#undoRemove, #removeImage').html('<i class="fas fa-trash"></i> ลบรูปภาพ')
                    .removeClass('btn-warning').addClass('btn-danger').attr('id', 'removeImage');
            };
            reader.readAsDataURL(file);
        }
    });

    // Remove image
    $(document).on('click', '#removeImage', function() {
        Swal.fire({
            title: 'ลบรูปภาพ?',
            text: 'คุณแน่ใจหรือไม่ที่จะลบรูปภาพนี้?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#6c757d',
            confirmButtonText: '<i class="fas fa-trash"></i> ลบเลย',
            cancelButtonText: '<i class="fas fa-times"></i> ยกเลิก',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                // ตั้งค่า flag ให้ลบรูปภาพ
                $('#removeImageFlag').val('1');
                $('#image').val('');

                // ซ่อนรูปภาพและแสดงข้อความไม่มีรูป
                $('#imagePreview').hide();
                $('#noImageDisplay').show();

                // ลบข้อความชื่อไฟล์ปัจจุบัน
                $('#currentFileName').html(`
                    <small class="text-danger">
                        <i class="fas fa-trash"></i> รูปภาพจะถูกลบเมื่อกดบันทึก
                    </small>
                `);

                // เปลี่ยนข้อความปุ่ม
                $('#removeImage').html('<i class="fas fa-undo"></i> ยกเลิกการลบ').removeClass('btn-danger').addClass('btn-warning');
                $('#removeImage').attr('id', 'undoRemove');

                // แสดงข้อความแจ้งเตือน
                Swal.fire({
                    title: 'ลบรูปภาพแล้ว!',
                    text: 'รูปภาพจะถูกลบเมื่อคุณกดบันทึก',
                    icon: 'success',
                    timer: 2000,
                    showConfirmButton: false
                });
            }
        });
    });

    // Undo remove image
    $(document).on('click', '#undoRemove', function() {
        Swal.fire({
            title: 'ยกเลิกการลบ?',
            text: 'คุณต้องการยกเลิกการลบรูปภาพหรือไม่?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#28a745',
            cancelButtonColor: '#6c757d',
            confirmButtonText: '<i class="fas fa-undo"></i> ยกเลิกการลบ',
            cancelButtonText: '<i class="fas fa-times"></i> ไม่ต้อง',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                // ยกเลิกการลบ
                $('#removeImageFlag').val('0');

                if (hasImage) {
                    $('#imagePreview').show();
                    $('#noImageDisplay').hide();
                    $('#previewImg').attr('src', originalImageUrl);

                    // กลับไปแสดงชื่อไฟล์เดิม
                    if (originalImageName) {
                        $('#currentFileName').html(`
                            <small class="text-primary">
                                <i class="fas fa-file-image"></i> ไฟล์ปัจจุบัน: <strong>${originalImageName}</strong>
                            </small>
                        `);
                    }
                }

                // เปลี่ยนปุ่มกลับ
                $('#undoRemove').html('<i class="fas fa-trash"></i> ลบรูปภาพ').removeClass('btn-warning').addClass('btn-danger');
                $('#undoRemove').attr('id', 'removeImage');

                // แสดงข้อความแจ้งเตือน
                Swal.fire({
                    title: 'ยกเลิกแล้ว!',
                    text: 'รูปภาพจะไม่ถูกลบ',
                    icon: 'success',
                    timer: 1500,
                    showConfirmButton: false
                });
            }
        });
    });

    // Form validation
    $('#packageForm').on('submit', function(e) {
        let isValid = true;
        
        // Clear previous errors
        $('.form-control').removeClass('is-invalid');
        $('.invalid-feedback').remove();
        
        // Validate name
        if (!$('#name').val().trim()) {
            $('#name').addClass('is-invalid');
            $('#name').after('<div class="invalid-feedback">กรุณากรอกชื่อแพ็กเกจ</div>');
            isValid = false;
        }
        
        // Validate description
        if (!$('#description').val().trim()) {
            $('#description').addClass('is-invalid');
            $('#description').after('<div class="invalid-feedback">กรุณากรอกรายละเอียดแพ็กเกจ</div>');
            isValid = false;
        }
        
        // Validate price
        if (!$('#price').val() || $('#price').val() < 0) {
            $('#price').addClass('is-invalid');
            $('#price').parent().after('<div class="invalid-feedback">กรุณากรอกราคาที่ถูกต้อง</div>');
            isValid = false;
        }
        
        if (!isValid) {
            e.preventDefault();
        }
    });

    // Auto-resize textarea
    $('#description').on('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });

    // Delete package
    $('#deletePackageBtn').on('click', function(e) {
        e.preventDefault();

        Swal.fire({
            title: 'ลบแพ็กเกจ?',
            html: `คุณแน่ใจหรือไม่ที่จะลบแพ็กเกจ<br><strong>"{{ $package->title }}"</strong>?<br><small class="text-muted">การกระทำนี้ไม่สามารถยกเลิกได้</small>`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#6c757d',
            confirmButtonText: '<i class="fas fa-trash"></i> ลบเลย',
            cancelButtonText: '<i class="fas fa-times"></i> ยกเลิก',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                // แสดง loading
                Swal.fire({
                    title: 'กำลังลบ...',
                    text: 'กรุณารอสักครู่',
                    icon: 'info',
                    allowOutsideClick: false,
                    showConfirmButton: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                // สร้าง form และ submit
                let form = $('<form>', {
                    'method': 'POST',
                    'action': '{{ route('admin.packages.destroy', $package) }}'
                });

                form.append($('<input>', {
                    'type': 'hidden',
                    'name': '_token',
                    'value': $('meta[name="csrf-token"]').attr('content')
                }));

                form.append($('<input>', {
                    'type': 'hidden',
                    'name': '_method',
                    'value': 'DELETE'
                }));

                $('body').append(form);
                form.submit();
            }
        });
    });
});
</script>
@endpush

@endsection
