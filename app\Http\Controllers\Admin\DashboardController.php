<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Helpers\DashboardHelper;
use App\Models\Service;
use App\Models\Package;
use App\Models\Activity;
use App\Models\Contact;
use App\Models\SiteSetting;

class DashboardController extends Controller
{
    /**
     * Display the admin dashboard.
     */
    public function index()
    {
        // Initialize default values
        $stats = DashboardHelper::getDefaultStatistics();
        $servicesCount = 0;
        $packagesCount = 0;
        $activitiesCount = 0;
        $unreadContactsCount = 0;
        $recentContacts = collect();
        $siteSettings = null;

        try {
            // Get statistics using DashboardHelper
            $stats = DashboardHelper::getStatistics();

            // Get additional data for dashboard
            $servicesCount = Service::count();
            $packagesCount = Package::count();
            $activitiesCount = Activity::count();
            $unreadContactsCount = Contact::where('is_read', false)->count();
            $recentContacts = Contact::latest()->take(5)->get();
            $siteSettings = SiteSetting::getSettings(); // Use getSettings() method

        } catch (\Exception $e) {
            \Log::error('Dashboard error: ' . $e->getMessage());
            // Keep default values already set above
        }

        return view('admin.dashboard', compact(
            'stats',
            'servicesCount',
            'packagesCount',
            'activitiesCount',
            'unreadContactsCount',
            'recentContacts',
            'siteSettings'
        ));
    }
}
