<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Service;
use App\Helpers\ImageHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class ServiceController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $services = Service::latest()->get();
        return view('admin.services.index', compact('services'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.services.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'price' => 'required|numeric|min:0',
            'image' => 'nullable|image|mimes:jpeg,jpg,png,gif,webp|max:2048',
        ]);

        $data = $request->only(['title', 'description', 'price']);

        // Handle image upload
        if ($request->hasFile('image')) {
            $data['image'] = ImageHelper::uploadAndResize($request->file('image'), 'services');
        }

        Service::create($data);

        return redirect()->route('admin.services.index')
            ->with('success', 'เพิ่มบริการสำเร็จ');
    }

    /**
     * Display the specified resource.
     */
    public function show(Service $service)
    {
        return view('admin.services.show', compact('service'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Service $service)
    {
        return view('admin.services.edit', compact('service'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Service $service)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'price' => 'required|numeric|min:0',
            'image' => 'nullable|image|mimes:jpeg,jpg,png,gif,webp|max:2048',
        ]);

        $data = $request->only(['title', 'description', 'price']);

        // Handle image removal
        if ($request->input('remove_image') == '1') {
            // Delete old image if exists
            if ($service->image && Storage::disk('public')->exists('services/' . $service->image)) {
                Storage::disk('public')->delete('services/' . $service->image);
            }
            // บังคับให้ลบรูปภาพ
            $service->image = null;
            $service->save();
        }
        // Handle image upload
        elseif ($request->hasFile('image')) {
            // Delete old image if exists
            if ($service->image && Storage::disk('public')->exists('services/' . $service->image)) {
                Storage::disk('public')->delete('services/' . $service->image);
            }

            $data['image'] = ImageHelper::uploadAndResize($request->file('image'), 'services');
        }

        // อัปเดทข้อมูลอื่นๆ
        $service->update($data);

        return redirect()->route('admin.services.edit', $service)
            ->with('success', 'อัปเดตบริการสำเร็จ');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Service $service)
    {
        // Delete image if exists
        if ($service->image && Storage::disk('public')->exists('services/' . $service->image)) {
            Storage::disk('public')->delete('services/' . $service->image);
        }

        $service->delete();

        return redirect()->route('admin.services.index')
            ->with('success', 'ลบบริการสำเร็จ');
    }

    /**
     * Bulk delete services
     */
    public function bulkDelete(Request $request)
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'exists:services,id'
        ]);

        $services = Service::whereIn('id', $request->ids)->get();
        
        foreach ($services as $service) {
            // Delete image if exists
            if ($service->image && Storage::disk('public')->exists('services/' . $service->image)) {
                Storage::disk('public')->delete('services/' . $service->image);
            }
            $service->delete();
        }

        return response()->json([
            'success' => true,
            'message' => 'ลบบริการที่เลือกสำเร็จ (' . count($request->ids) . ' รายการ)'
        ]);
    }
}
