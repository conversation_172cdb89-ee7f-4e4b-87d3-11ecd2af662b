@extends('layouts.app')

@section('title', $activity->title . ' - กิจกรรม - ' . config('app.name'))

@section('styles')
<link href="https://cdn.jsdelivr.net/npm/lightbox2@2.11.4/dist/css/lightbox.min.css" rel="stylesheet">
<style>
    .hero-image {
        height: 400px;
        object-fit: cover;
        border-radius: 15px;
    }
    .gallery-item {
        margin-bottom: 20px;
        border-radius: 10px;
        overflow: hidden;
        transition: all 0.3s ease;
        cursor: pointer;
    }
    .gallery-item:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.2);
    }
    .gallery-image {
        width: 100%;
        height: 200px;
        object-fit: cover;
        transition: transform 0.3s ease;
    }
    .gallery-item:hover .gallery-image {
        transform: scale(1.05);
    }
    .activity-info {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 40px;
    }
    .info-item {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        padding: 10px;
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    }
    .info-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        color: white;
    }
    .related-activity {
        transition: all 0.3s ease;
        border-radius: 10px;
        overflow: hidden;
    }
    .related-activity:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }
    .related-image {
        height: 150px;
        object-fit: cover;
    }
    .breadcrumb-custom {
        background: none;
        padding: 20px 0;
    }
    .breadcrumb-custom .breadcrumb-item + .breadcrumb-item::before {
        content: "›";
        font-size: 1.2rem;
        color: #6c757d;
    }
    .section-title {
        position: relative;
        display: inline-block;
        margin-bottom: 30px;
        font-weight: 700;
    }
    .section-title::after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 0;
        width: 60px;
        height: 3px;
        background: #007bff;
    }
    .masonry-grid {
        column-count: 3;
        column-gap: 20px;
    }
    .masonry-item {
        break-inside: avoid;
        margin-bottom: 20px;
    }
    @media (max-width: 768px) {
        .masonry-grid {
            column-count: 2;
        }
        .hero-image {
            height: 250px;
        }
    }
    @media (max-width: 576px) {
        .masonry-grid {
            column-count: 1;
        }
    }
</style>
@endsection

@section('content')
<div class="container">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="breadcrumb-custom">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ route('home') }}">หน้าแรก</a></li>
            <li class="breadcrumb-item"><a href="{{ route('activities.index') }}">กิจกรรม</a></li>
            <li class="breadcrumb-item active">{{ $activity->title }}</li>
        </ol>
    </nav>

    <!-- Activity Header -->
    <div class="row mb-5">
        <div class="col-lg-8">
            <img src="{{ $activity->cover_image_url }}" 
                 class="img-fluid hero-image w-100" 
                 alt="{{ $activity->title }}"
                 onerror="this.src='{{ asset('images/no-image.svg') }}'">
        </div>
        <div class="col-lg-4">
            <div class="activity-info h-100 d-flex flex-column justify-content-center">
                <h1 class="h2 fw-bold mb-4">{{ $activity->title }}</h1>
                
                @if($activity->category)
                <div class="info-item">
                    <div class="info-icon" style="background-color: {{ $activity->category->color }};">
                        <i class="fas fa-tag"></i>
                    </div>
                    <div>
                        <strong>หมวดหมู่:</strong><br>
                        <span class="text-muted">{{ $activity->category->name }}</span>
                    </div>
                </div>
                @endif
                
                @if($activity->activity_date)
                <div class="info-item">
                    <div class="info-icon" style="background-color: #28a745;">
                        <i class="fas fa-calendar"></i>
                    </div>
                    <div>
                        <strong>วันที่จัดกิจกรรม:</strong><br>
                        <span class="text-muted">{{ $activity->activity_date->format('d/m/Y') }}</span>
                    </div>
                </div>
                @endif
                
                @if($activity->location)
                <div class="info-item">
                    <div class="info-icon" style="background-color: #dc3545;">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <div>
                        <strong>สถานที่:</strong><br>
                        <span class="text-muted">{{ $activity->location }}</span>
                    </div>
                </div>
                @endif
                
                <div class="info-item">
                    <div class="info-icon" style="background-color: #17a2b8;">
                        <i class="fas fa-images"></i>
                    </div>
                    <div>
                        <strong>จำนวนรูปภาพ:</strong><br>
                        <span class="text-muted">{{ $activity->images->count() }} รูป</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Activity Description -->
    @if($activity->description)
    <div class="row mb-5">
        <div class="col-12">
            <h3 class="section-title">รายละเอียดกิจกรรม</h3>
            <div class="bg-light p-4 rounded">
                <p class="mb-0 lh-lg">{{ $activity->description }}</p>
            </div>
        </div>
    </div>
    @endif

    <!-- Photo Gallery -->
    @if($activity->images->count() > 0)
    <div class="row mb-5">
        <div class="col-12">
            <h3 class="section-title">แกลอรี่รูปภาพ</h3>
            <div class="masonry-grid">
                @foreach($activity->images as $image)
                <div class="masonry-item">
                    <div class="gallery-item">
                        <a href="{{ $image->image_url }}" 
                           data-lightbox="activity-gallery" 
                           data-title="{{ $image->caption ?: $activity->title }}">
                            <img src="{{ $image->image_url }}" 
                                 class="gallery-image" 
                                 alt="{{ $image->caption ?: $activity->title }}"
                                 onerror="this.src='{{ asset('images/no-image.svg') }}'">
                        </a>
                        @if($image->caption)
                        <div class="p-3 bg-white">
                            <small class="text-muted">{{ $image->caption }}</small>
                        </div>
                        @endif
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </div>
    @endif

    <!-- Related Activities -->
    @if($relatedActivities->count() > 0)
    <div class="row mb-5">
        <div class="col-12">
            <h3 class="section-title">กิจกรรมที่เกี่ยวข้อง</h3>
            <div class="row">
                @foreach($relatedActivities as $related)
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card related-activity h-100">
                        <img src="{{ $related->cover_image_url }}" 
                             class="card-img-top related-image" 
                             alt="{{ $related->title }}"
                             onerror="this.src='{{ asset('images/no-image.svg') }}'">
                        <div class="card-body">
                            <h6 class="card-title">{{ $related->title }}</h6>
                            <p class="card-text small text-muted">{{ Str::limit($related->description, 80) }}</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    <i class="fas fa-images me-1"></i>{{ $related->images->count() }} รูป
                                </small>
                                <a href="{{ route('activities.show', $related) }}" class="btn btn-sm btn-outline-primary">
                                    ดูเพิ่มเติม
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </div>
    @endif

    <!-- Back Button -->
    <div class="row">
        <div class="col-12 text-center">
            <a href="{{ route('activities.index') }}" class="btn btn-outline-secondary btn-lg">
                <i class="fas fa-arrow-left me-2"></i>กลับไปหน้ากิจกรรม
            </a>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/lightbox2@2.11.4/dist/js/lightbox.min.js"></script>
<script>
// Configure Lightbox
lightbox.option({
    'resizeDuration': 200,
    'wrapAround': true,
    'albumLabel': 'รูปที่ %1 จาก %2',
    'fadeDuration': 300,
    'imageFadeDuration': 300
});

// Smooth scroll animation for gallery items
document.addEventListener('DOMContentLoaded', function() {
    const galleryItems = document.querySelectorAll('.gallery-item');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                setTimeout(() => {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }, index * 50);
            }
        });
    });
    
    galleryItems.forEach(item => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(20px)';
        item.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
        observer.observe(item);
    });
});
</script>
@endsection
