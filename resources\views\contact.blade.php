@extends('layouts.app')

@section('title', 'ติดต่อเรา - บริการงานศพครบวงจร')

@section('content')
<!-- Hero Section -->
<section class="hero-section text-white parallax-bg">
    <!-- Particle Background -->
    <div class="particles-bg" id="particles-bg-contact"></div>
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold mb-4">
                    <i class="fas fa-phone me-3" style="color: var(--memorial-gold);"></i>ติดต่อเรา
                </h1>
                <p class="lead mb-4">ทีมงานของเราพร้อมให้คำปรึกษาและดูแลทุกรายละเอียด ตลอด 24 ชั่วโมง</p>
                <a href="#contact-info" class="btn btn-light btn-lg px-4">
                    <i class="fas fa-arrow-down me-2"></i>ดูข้อมูลติดต่อ
                </a>
            </div>
            <div class="col-lg-6 text-center">
                <i class="fas fa-phone fa-10x opacity-75 floating-animation" style="color: var(--memorial-gold);"></i>
            </div>
        </div>
    </div>
</section>

<!-- Contact Info Section -->
<section id="contact-info" class="py-5">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="section-title">ข้อมูลติดต่อ</h2>
            <p class="text-muted lead">ติดต่อเราได้หลายช่องทาง พร้อมให้คำปรึกษาตลอด 24 ชั่วโมง</p>
        </div>

        <div class="row g-4">
            <div class="col-lg-4">
                <div class="card memorial-card h-100 shadow-sm text-center">
                    <div class="card-body">
                        <div class="mb-3">
                            <i class="fas fa-phone fa-3x" style="color: var(--memorial-gold);"></i>
                        </div>
                        <h5 class="card-title fw-bold">โทรศัพท์</h5>
                        <p class="card-text text-muted">ติดต่อเราได้ตลอด 24 ชั่วโมง<br>พร้อมให้คำปรึกษา</p>
                        <a href="tel:{{ isset($siteSettings) ? $siteSettings->contact_phone : '02-123-4567' }}" class="btn btn-memorial">
                            <i class="fas fa-phone me-2"></i>{{ isset($siteSettings) ? $siteSettings->contact_phone : '02-123-4567' }}
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card memorial-card h-100 shadow-sm text-center">
                    <div class="card-body">
                        <div class="mb-3">
                            <i class="fas fa-envelope fa-3x" style="color: var(--memorial-gold);"></i>
                        </div>
                        <h5 class="card-title fw-bold">อีเมล</h5>
                        <p class="card-text text-muted">ส่งข้อความถึงเราได้เลย<br>ตอบกลับภายใน 24 ชั่วโมง</p>
                        <a href="mailto:{{ isset($siteSettings) ? $siteSettings->contact_email : '<EMAIL>' }}" class="btn btn-memorial">
                            <i class="fas fa-envelope me-2"></i>{{ isset($siteSettings) ? $siteSettings->contact_email : '<EMAIL>' }}
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card memorial-card h-100 shadow-sm text-center">
                    <div class="card-body">
                        <div class="mb-3">
                            <i class="fas fa-map-marker-alt fa-3x" style="color: var(--memorial-gold);"></i>
                        </div>
                        <h5 class="card-title fw-bold">ที่อยู่</h5>
                        <p class="card-text text-muted">สำนักงานใหญ่<br>{{ isset($siteSettings) ? Str::limit($siteSettings->contact_address, 30) : 'กรุงเทพมหานคร' }}</p>
                        <a href="#" class="btn btn-memorial">
                            <i class="fas fa-map me-2"></i>ดูแผนที่
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Memorial Divider -->
<div class="memorial-divider"></div>

<!-- Contact Form Section -->
<section class="bg-light py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="card shadow-sm">
                    <div class="card-body p-5">
                        @if(session('success'))
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        @endif
                        
                        @if($errors->any())
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <i class="fas fa-exclamation-triangle me-2"></i>กรุณาตรวจสอบข้อมูลที่กรอก
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        @endif
                        
                        <div class="text-center mb-4">
                            <h3 class="fw-bold text-primary">ส่งข้อความถึงเรา</h3>
                            <p class="text-muted">กรอกข้อมูลด้านล่างเพื่อติดต่อเรา ทีมงานจะติดต่อกลับภายใน 24 ชั่วโมง</p>
                        </div>
                        
                        <form action="{{ route('contact.store') }}" method="POST">
                            @csrf
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="name" class="form-label fw-bold">ชื่อ-นามสกุล *</label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" value="{{ old('name') }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-md-6">
                                    <label for="phone" class="form-label fw-bold">เบอร์โทรศัพท์ *</label>
                                    <input type="tel" class="form-control @error('phone') is-invalid @enderror" id="phone" name="phone" value="{{ old('phone') }}" required>
                                    @error('phone')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-12">
                                    <label for="email" class="form-label fw-bold">อีเมล *</label>
                                    <input type="email" class="form-control @error('email') is-invalid @enderror" id="email" name="email" value="{{ old('email') }}" required>
                                    @error('email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-12">
                                    <label for="address" class="form-label fw-bold">ที่อยู่</label>
                                    <textarea class="form-control @error('address') is-invalid @enderror" id="address" name="address" rows="3">{{ old('address') }}</textarea>
                                    @error('address')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-12">
                                    <label for="message" class="form-label fw-bold">ข้อความ *</label>
                                    <textarea class="form-control @error('message') is-invalid @enderror" id="message" name="message" rows="5" required>{{ old('message') }}</textarea>
                                    @error('message')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-12 text-center">
                                    <button type="submit" class="btn btn-memorial btn-lg px-5">
                                        <i class="fas fa-paper-plane me-2"></i>ส่งข้อความ
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Memorial Divider -->
<div class="memorial-divider"></div>

<!-- Social Media Section -->
<section class="py-5">
    <div class="container">
        <div class="text-center">
            <h3 class="section-title">ติดตามเรา</h3>
            <p class="text-muted mb-4">ติดตามข่าวสารและข้อมูลบริการของเรา</p>
            <div class="d-flex justify-content-center gap-3">
                @if(isset($siteSettings) && $siteSettings->facebook_url)
                    <a href="{{ $siteSettings->facebook_url }}" target="_blank" class="btn btn-outline-primary btn-lg">
                        <i class="fab fa-facebook fa-2x"></i>
                    </a>
                @endif
                @if(isset($siteSettings) && $siteSettings->line_url)
                    <a href="{{ $siteSettings->line_url }}" target="_blank" class="btn btn-outline-success btn-lg">
                        <i class="fab fa-line fa-2x"></i>
                    </a>
                @endif
                @if(isset($siteSettings) && $siteSettings->instagram_url)
                    <a href="{{ $siteSettings->instagram_url }}" target="_blank" class="btn btn-outline-danger btn-lg">
                        <i class="fab fa-instagram fa-2x"></i>
                    </a>
                @endif
            </div>
        </div>
    </div>
</section>
@endsection 