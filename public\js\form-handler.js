/**
 * Form Handler for SoloShop Admin
 * Handles form submissions with proper error handling and JSON response parsing
 */

class FormHandler {
    constructor() {
        this.init();
    }

    init() {
        // Handle all form submissions with file uploads
        document.addEventListener('submit', (e) => {
            const form = e.target;
            
            // Only handle forms with enctype="multipart/form-data"
            if (form.enctype === 'multipart/form-data') {
                this.handleFormSubmission(e);
            }
        });
    }

    handleFormSubmission(event) {
        const form = event.target;
        const submitButton = form.querySelector('button[type="submit"]');
        
        // Prevent double submission
        if (submitButton) {
            submitButton.disabled = true;
            const originalText = submitButton.innerHTML;
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> กำลังบันทึก...';
            
            // Re-enable button after 5 seconds as fallback
            setTimeout(() => {
                submitButton.disabled = false;
                submitButton.innerHTML = originalText;
            }, 5000);
        }

        // Let the form submit normally, but add error handling
        this.addErrorHandling(form);
    }

    addErrorHandling(form) {
        // Add hidden input to indicate we want JSON response on error
        const jsonInput = document.createElement('input');
        jsonInput.type = 'hidden';
        jsonInput.name = '_wants_json';
        jsonInput.value = '1';
        form.appendChild(jsonInput);
    }

    /**
     * Handle AJAX form submission
     */
    static submitFormAjax(form, options = {}) {
        return new Promise((resolve, reject) => {
            const formData = new FormData(form);
            const url = form.action || window.location.href;
            const method = form.method || 'POST';

            // Add CSRF token if not present
            if (!formData.has('_token')) {
                const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
                if (csrfToken) {
                    formData.append('_token', csrfToken);
                }
            }

            fetch(url, {
                method: method,
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json',
                    ...options.headers
                }
            })
            .then(response => {
                const contentType = response.headers.get('content-type');
                
                if (contentType && contentType.includes('application/json')) {
                    return response.json().then(data => ({ data, response }));
                } else {
                    // If not JSON, it might be a redirect or HTML error page
                    return response.text().then(html => {
                        if (response.ok) {
                            // Successful submission, probably a redirect
                            window.location.reload();
                            return { data: { success: true }, response };
                        } else {
                            // Error page
                            throw new Error('เกิดข้อผิดพลาดในการประมวลผล กรุณาลองใหม่อีกครั้ง');
                        }
                    });
                }
            })
            .then(({ data, response }) => {
                if (response.ok && data.success !== false) {
                    resolve(data);
                } else {
                    reject(new Error(data.message || 'เกิดข้อผิดพลาดในการบันทึกข้อมูล'));
                }
            })
            .catch(error => {
                console.error('Form submission error:', error);
                reject(error);
            });
        });
    }

    /**
     * Show error message
     */
    static showError(message, container = null) {
        const alertHtml = `
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle"></i> ${message}
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        `;

        if (container) {
            container.innerHTML = alertHtml + container.innerHTML;
        } else {
            // Find the first .card-body or .content element
            const target = document.querySelector('.card-body, .content');
            if (target) {
                target.insertAdjacentHTML('afterbegin', alertHtml);
            }
        }

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            const alert = document.querySelector('.alert-danger');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }

    /**
     * Show success message
     */
    static showSuccess(message, container = null) {
        const alertHtml = `
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle"></i> ${message}
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        `;

        if (container) {
            container.innerHTML = alertHtml + container.innerHTML;
        } else {
            // Find the first .card-body or .content element
            const target = document.querySelector('.card-body, .content');
            if (target) {
                target.insertAdjacentHTML('afterbegin', alertHtml);
            }
        }

        // Auto-dismiss after 3 seconds
        setTimeout(() => {
            const alert = document.querySelector('.alert-success');
            if (alert) {
                alert.remove();
            }
        }, 3000);
    }

    /**
     * Validate file input
     */
    static validateFile(file, options = {}) {
        const defaults = {
            maxSize: 2 * 1024 * 1024, // 2MB
            allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
        };

        const config = { ...defaults, ...options };
        const errors = [];

        if (file.size > config.maxSize) {
            errors.push(`ขนาดไฟล์ต้องไม่เกิน ${config.maxSize / (1024 * 1024)}MB`);
        }

        if (!config.allowedTypes.includes(file.type)) {
            errors.push('ไฟล์ต้องเป็นรูปภาพ (JPEG, JPG, PNG, GIF, WebP)');
        }

        return errors;
    }
}

// Initialize form handler when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new FormHandler();
});

// Export for use in other scripts
window.FormHandler = FormHandler;
