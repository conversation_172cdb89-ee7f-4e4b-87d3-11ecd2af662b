@extends('layouts.auth')

@section('title', 'ตั้งรหัสผ่านใหม่ - SoloShop')

@section('content')
<form method="POST" action="{{ route('password.update') }}" id="resetPasswordForm">
    @csrf

    <input type="hidden" name="token" value="{{ $token }}">

    @if($errors->any())
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>
            กรุณาตรวจสอบข้อมูลที่กรอก
        </div>
    @endif

    <div class="text-center mb-4">
        <h4 class="text-primary">ตั้งรหัสผ่านใหม่</h4>
        <p class="text-muted">กรอกรหัสผ่านใหม่ของคุณ</p>
    </div>

    <div class="form-group">
        <input id="email"
               type="email"
               class="form-control @error('email') is-invalid @enderror"
               name="email"
               value="{{ $email ?? old('email') }}"
               required
               autocomplete="email"
               autofocus
               placeholder="อีเมล">
        @error('email')
            <div class="invalid-feedback d-block">
                {{ $message }}
            </div>
        @enderror
    </div>

    <div class="form-group">
        <input id="password"
               type="password"
               class="form-control @error('password') is-invalid @enderror"
               name="password"
               required
               autocomplete="new-password"
               placeholder="รหัสผ่านใหม่">
        @error('password')
            <div class="invalid-feedback d-block">
                {{ $message }}
            </div>
        @enderror
    </div>

    <div class="form-group">
        <input id="password-confirm"
               type="password"
               class="form-control"
               name="password_confirmation"
               required
               autocomplete="new-password"
               placeholder="ยืนยันรหัสผ่านใหม่">
    </div>

    <div class="d-grid mb-3">
        <button type="submit" class="btn btn-primary" id="updateBtn">
            อัปเดตรหัสผ่าน
        </button>
    </div>

    <div class="text-center">
        <a href="{{ route('login') }}" class="text-decoration-none">
            <small class="text-muted">
                <i class="fas fa-arrow-left me-1"></i>
                กลับไปหน้าเข้าสู่ระบบ
            </small>
        </a>
    </div>
</form>

@push('scripts')
<script>
document.getElementById('resetPasswordForm').addEventListener('submit', function() {
    const updateBtn = document.getElementById('updateBtn');
    updateBtn.classList.add('btn-loading');
    updateBtn.disabled = true;
    updateBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>กำลังอัปเดต...';
});
</script>
@endpush
@endsection
