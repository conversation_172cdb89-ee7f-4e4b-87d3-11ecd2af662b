@extends('layouts.auth')

@section('title', 'รีเซ็ตรหัสผ่าน - SoloShop')

@section('content')
<form method="POST" action="{{ route('password.email') }}" id="forgotPasswordForm">
    @csrf

    @if (session('status'))
        <div class="alert alert-success">
            <i class="fas fa-check-circle me-2"></i>
            {{ session('status') }}
        </div>
    @endif

    @if($errors->any())
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>
            กรุณาตรวจสอบข้อมูลที่กรอก
        </div>
    @endif

    <div class="text-center mb-4">
        <h4 class="text-primary">รีเซ็ตรหัสผ่าน</h4>
        <p class="text-muted">กรอกอีเมลของคุณเพื่อรับลิงก์รีเซ็ตรหัสผ่าน</p>
    </div>

    <div class="form-group">
        <input id="email"
               type="email"
               class="form-control @error('email') is-invalid @enderror"
               name="email"
               value="{{ old('email') }}"
               required
               autocomplete="email"
               autofocus
               placeholder="อีเมล">
        @error('email')
            <div class="invalid-feedback d-block">
                {{ $message }}
            </div>
        @enderror
    </div>

    <div class="d-grid mb-3">
        <button type="submit" class="btn btn-primary" id="resetBtn">
            ส่งลิงก์รีเซ็ตรหัสผ่าน
        </button>
    </div>

    <div class="text-center">
        <a href="{{ route('login') }}" class="text-decoration-none">
            <small class="text-muted">
                <i class="fas fa-arrow-left me-1"></i>
                กลับไปหน้าเข้าสู่ระบบ
            </small>
        </a>
    </div>
</form>

@push('scripts')
<script>
document.getElementById('forgotPasswordForm').addEventListener('submit', function() {
    const resetBtn = document.getElementById('resetBtn');
    resetBtn.classList.add('btn-loading');
    resetBtn.disabled = true;
    resetBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>กำลังส่ง...';
});
</script>
@endpush
@endsection
