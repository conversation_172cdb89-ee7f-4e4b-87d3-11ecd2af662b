<?php
// Debug script to check <PERSON><PERSON> routing
echo "<h1><PERSON><PERSON> Debug</h1>";

// Check if <PERSON><PERSON> can be loaded
try {
    require_once '../vendor/autoload.php';
    $app = require_once '../bootstrap/app.php';
    
    echo "<p style='color: green;'>✓ Laravel application loaded successfully</p>";
    
    // Get the kernel
    $kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
    
    // Create a request for the home route
    $request = Illuminate\Http\Request::create('/', 'GET');
    
    echo "<p>Testing home route...</p>";
    
    // Handle the request
    $response = $kernel->handle($request);
    
    echo "<p>Response Status: " . $response->getStatusCode() . "</p>";
    echo "<p>Response Headers:</p>";
    echo "<pre>";
    foreach ($response->headers->all() as $key => $values) {
        echo $key . ": " . implode(', ', $values) . "\n";
    }
    echo "</pre>";
    
    if ($response->getStatusCode() === 200) {
        echo "<p style='color: green;'>✓ Home route works correctly</p>";
        echo "<h3>Response Content (first 500 chars):</h3>";
        echo "<pre>" . htmlspecialchars(substr($response->getContent(), 0, 500)) . "...</pre>";
    } else {
        echo "<p style='color: red;'>✗ Home route returned status: " . $response->getStatusCode() . "</p>";
        echo "<h3>Response Content:</h3>";
        echo "<pre>" . htmlspecialchars($response->getContent()) . "</pre>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<h2>Server Information</h2>";
echo "<p>PHP Version: " . phpversion() . "</p>";
echo "<p>Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p>Script Name: " . $_SERVER['SCRIPT_NAME'] . "</p>";
echo "<p>Request URI: " . $_SERVER['REQUEST_URI'] . "</p>";
echo "<p>HTTP Host: " . $_SERVER['HTTP_HOST'] . "</p>";
?>
