/* Admin Activities Edit Page Styles */

/* Gallery Card Improvements */
.gallery-card {
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.gallery-card:hover {
    border-color: #007bff;
    box-shadow: 0 4px 12px rgba(0,123,255,0.3);
    transform: translateY(-2px);
}

.gallery-card .card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
}

/* Drag Handle */
.drag-handle {
    cursor: move;
    color: #6c757d;
    transition: color 0.2s ease;
}

.drag-handle:hover {
    color: #495057;
}

/* Sortable States */
.sortable-ghost {
    opacity: 0.5;
    transform: scale(0.95);
}

.sortable-chosen {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.sortable-drag {
    transform: rotate(5deg);
}

/* Gallery Item Container */
#sortable-gallery .gallery-item-container {
    cursor: move;
    transition: all 0.3s ease;
}

#sortable-gallery .gallery-item-container:hover {
    transform: scale(1.02);
}

/* Gallery Image */
.gallery-image {
    transition: all 0.3s ease;
    border-radius: 0.375rem;
}

.gallery-image:hover {
    transform: scale(1.05);
    filter: brightness(1.1);
}

/* Button Groups */
.btn-group-sm .btn {
    transition: all 0.2s ease;
}

.btn-group-sm .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Loading States */
.btn.loading {
    position: relative;
    color: transparent !important;
}

.btn.loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Modal Improvements */
.modal-content {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border-radius: 0.5rem 0.5rem 0 0;
}

.modal-header .modal-title {
    font-weight: 600;
}

.modal-header .btn-close {
    filter: invert(1);
}

/* Form Improvements */
.custom-file-label {
    transition: all 0.2s ease;
}

.custom-file-input:focus ~ .custom-file-label {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

/* Preview Improvements */
.image-preview {
    border: 2px dashed #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    text-align: center;
    transition: all 0.3s ease;
}

.image-preview:hover {
    border-color: #007bff;
    background-color: #f8f9fa;
}

.image-preview img {
    border-radius: 0.375rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* File Info Alerts */
.alert {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

/* Toast Notifications */
.success-toast,
.error-toast {
    animation: slideInRight 0.3s ease-out;
    border: none;
    border-radius: 0.5rem;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Caption Display */
.caption-display {
    min-height: 2rem;
    display: flex;
    align-items: center;
}

.caption-display p {
    margin: 0;
    line-height: 1.4;
}

/* Card Footer */
.card-footer {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-top: 1px solid #dee2e6;
}

/* Responsive Improvements */
@media (max-width: 768px) {
    .gallery-item-container {
        margin-bottom: 1rem;
    }
    
    .btn-group-sm {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
    }
    
    .modal-dialog {
        margin: 1rem;
    }
}

/* Focus States */
.btn:focus,
.form-control:focus,
.custom-file-input:focus ~ .custom-file-label {
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

/* Smooth Transitions */
* {
    transition: box-shadow 0.15s ease-in-out;
}

/* Error States */
.is-invalid {
    border-color: #dc3545;
}

.is-invalid:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220,53,69,0.25);
}

/* Success States */
.is-valid {
    border-color: #28a745;
}

.is-valid:focus {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40,167,69,0.25);
}

/* Fix toggle switch z-index issue */
.custom-control-input {
    z-index: 1 !important;
}

.custom-control-label {
    z-index: 1 !important;
}

.custom-control-label::before,
.custom-control-label::after {
    z-index: 1 !important;
}

/* Ensure buttons have higher z-index than toggle switches */
.btn {
    position: relative;
    z-index: 10 !important;
}

.card-footer .btn {
    z-index: 15 !important;
}

/* Fix for floating elements that might overlap buttons */
.card-footer {
    position: relative;
    z-index: 20 !important;
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
    padding: 1rem 1.25rem;
}

/* Ensure form elements don't overlap buttons */
.form-group {
    position: relative;
    z-index: 1;
}

/* Fix for any floating or absolute positioned elements */
.custom-control {
    position: relative;
    z-index: 1;
}

/* Additional spacing to prevent overlap */
.card-footer {
    margin-top: 1rem;
    clear: both;
}

/* Force proper stacking order for all elements */
.custom-switch {
    position: relative !important;
    z-index: 1 !important;
}

.custom-switch .custom-control-input {
    position: relative !important;
    z-index: 1 !important;
}

.custom-switch .custom-control-label {
    position: relative !important;
    z-index: 1 !important;
}

.custom-switch .custom-control-label::before,
.custom-switch .custom-control-label::after {
    position: absolute !important;
    z-index: 1 !important;
}

/* Ensure buttons are always on top */
.btn-success,
.btn-secondary,
.btn-info {
    position: relative !important;
    z-index: 1000 !important;
}

/* Fix any potential overflow issues */
.card-body {
    overflow: visible !important;
}

.form-group {
    overflow: visible !important;
}

/* Ensure proper spacing between form elements and buttons */
.card-footer {
    margin-top: 2rem !important;
    padding-top: 1.5rem !important;
    border-top: 2px solid #dee2e6 !important;
}

/* Additional clearfix */
.card-footer::before {
    content: "";
    display: table;
    clear: both;
}

/* Responsive button layout */
.card-footer .d-flex {
    flex-wrap: wrap;
    gap: 0.5rem;
}

@media (max-width: 576px) {
    .card-footer .d-flex {
        flex-direction: column;
    }

    .card-footer .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}

/* Utility Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}
