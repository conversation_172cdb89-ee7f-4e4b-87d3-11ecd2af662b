<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Service extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'price',
        'image',
    ];

    protected $casts = [
        'price' => 'decimal:2',
    ];

    /**
     * Get the image URL attribute
     */
    public function getImageUrlAttribute()
    {
        return \App\Helpers\ImageHelper::getImageUrl($this->image ? 'services/' . $this->image : null);
    }

    /**
     * Get formatted price
     */
    public function getFormattedPriceAttribute()
    {
        return '฿' . number_format($this->price, 0);
    }

    /**
     * Get image file size in human readable format
     */
    public function getImageFileSizeAttribute()
    {
        if (!$this->image) {
            return null;
        }

        $filePath = storage_path('app/public/services/' . $this->image);

        if (!file_exists($filePath)) {
            return 'ไฟล์ไม่พบ';
        }

        $bytes = filesize($filePath);

        if ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' bytes';
        }
    }

    /**
     * Get image dimensions
     */
    public function getImageDimensionsAttribute()
    {
        if (!$this->image) {
            return null;
        }

        $filePath = storage_path('app/public/services/' . $this->image);

        if (!file_exists($filePath)) {
            return 'ไฟล์ไม่พบ';
        }

        try {
            $imageInfo = getimagesize($filePath);
            if ($imageInfo) {
                return $imageInfo[0] . ' × ' . $imageInfo[1] . ' px';
            }
        } catch (\Exception $e) {
            return 'ไม่สามารถอ่านข้อมูลได้';
        }

        return 'ไม่ทราบ';
    }
}
