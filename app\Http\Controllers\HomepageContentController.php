<?php

namespace App\Http\Controllers;

use App\Models\HomepageContent;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class HomepageContentController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $homepage_contents = \App\Models\HomepageContent::all();
        return view('admin.homepage.index', compact('homepage_contents'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\HomepageContent  $homepageContent
     * @return \Illuminate\Http\Response
     */
    public function show(HomepageContent $homepageContent)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\HomepageContent  $homepageContent
     * @return \Illuminate\Http\Response
     */
    public function edit(HomepageContent $homepageContent)
    {
        $homepage_content = $homepageContent;
        return view('admin.homepage.edit', compact('homepage_content'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\HomepageContent  $homepageContent
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, HomepageContent $homepageContent)
    {
        $validated = $request->validate([
            'section' => 'nullable|string|max:255',
            'title' => 'nullable|string|max:255',
            'content' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
            'button_text' => 'nullable|string|max:255',
            'button_link' => 'nullable|string|max:255',
            'remove_image' => 'nullable|string',
        ]);

        $data = $request->only(['section', 'title', 'content', 'button_text', 'button_link']);
        $imageRemoved = false;
        $newImageUrl = null;
        $newImageName = null;

        // Handle image removal
        if ($request->input('remove_image') == '1') {
            // Delete old image if exists
            if ($homepageContent->image) {
                Storage::disk('public')->delete($homepageContent->image);
            }
            $data['image'] = null;
            $imageRemoved = true;
        }
        // Handle image upload
        elseif ($request->hasFile('image')) {
            // Delete old image if exists
            if ($homepageContent->image) {
                Storage::disk('public')->delete($homepageContent->image);
            }

            $imagePath = $request->file('image')->store('homepage', 'public');
            $data['image'] = $imagePath;
            $newImageUrl = \App\Helpers\ImageHelper::getImageUrl($imagePath);
            $newImageName = basename($imagePath);
        }

        $homepageContent->update($data);

        // Return JSON response for AJAX requests
        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'อัปเดตเนื้อหาสำเร็จ',
                'section' => $homepageContent->section,
                'image_removed' => $imageRemoved,
                'image_url' => $newImageUrl,
                'image_name' => $newImageName,
            ]);
        }

        return redirect()->route('admin.homepage.index')->with('success', 'อัปเดตเนื้อหาหน้าแรกเรียบร้อยแล้ว');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\HomepageContent  $homepageContent
     * @return \Illuminate\Http\Response
     */
    public function destroy(HomepageContent $homepageContent)
    {
        //
    }
}
