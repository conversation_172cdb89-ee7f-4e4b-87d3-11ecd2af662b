<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // ลบ admin user เก่าก่อน (ถ้ามี)
        User::where('email', '<EMAIL>')->delete();

        // สร้าง admin user ใหม่
        $admin = User::create([
            'name' => 'Admin SoloShop',
            'email' => '<EMAIL>',
            'password' => Hash::make('admin123'),
            'is_admin' => true,
            'email_verified_at' => now(),
        ]);

        $this->command->info('Admin user created successfully!');
        $this->command->info('Email: <EMAIL>');
        $this->command->info('Password: admin123');
        $this->command->info('Admin status: ' . ($admin->is_admin ? 'true' : 'false'));
        $this->command->info('User ID: ' . $admin->id);
    }
} 