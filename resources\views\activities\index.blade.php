@extends('layouts.app')

@section('title', 'ภาพกิจกรรม - บริการงานศพครบวงจร')

@section('styles')
<style>
    .activity-card {
        transition: all 0.3s ease;
        border: none;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    .activity-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 15px 35px rgba(0,0,0,0.2);
    }
    .activity-image {
        height: 250px;
        object-fit: cover;
        transition: transform 0.3s ease;
    }
    .activity-card:hover .activity-image {
        transform: scale(1.05);
    }
    .category-badge {
        position: absolute;
        top: 15px;
        left: 15px;
        z-index: 2;
        border-radius: 20px;
        padding: 5px 15px;
        font-size: 0.8rem;
        font-weight: 600;
    }
    .image-count-badge {
        position: absolute;
        bottom: 15px;
        right: 15px;
        background: rgba(0,0,0,0.7);
        color: white;
        border-radius: 20px;
        padding: 5px 12px;
        font-size: 0.85rem;
        z-index: 2;
    }
    .filter-btn {
        border-radius: 25px;
        padding: 8px 20px;
        margin: 5px;
        border: 2px solid #e9ecef;
        background: white;
        color: #6c757d;
        transition: all 0.3s ease;
    }
    .filter-btn:hover, .filter-btn.active {
        background: #007bff;
        color: white;
        border-color: #007bff;
        transform: translateY(-2px);
    }
    .hero-section {
        background: linear-gradient(135deg, #000000 0%, #1a1a1a 50%, #2c2c2c 100%);
        color: white;
        padding: 80px 0;
        margin-bottom: 50px;
        border-bottom: 3px solid var(--memorial-gold);
        position: relative;
        overflow: hidden;
    }
    .section-title {
        position: relative;
        display: inline-block;
        margin-bottom: 30px;
    }
    .section-title::after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 80px;
        height: 3px;
        background: linear-gradient(135deg, var(--memorial-gold) 0%, var(--memorial-silver) 100%);
    }
</style>
@endsection

@section('content')
<!-- Hero Section -->
<div class="hero-section parallax-bg">
    <!-- Particle Background -->
    <div class="particles-bg" id="particles-bg-activities"></div>
    <div class="container">
        <div class="row justify-content-center text-center">
            <div class="col-lg-8">
                <h1 class="display-4 fw-bold mb-4">
                    <i class="fas fa-images me-3" style="color: var(--memorial-gold);"></i>ภาพความทรงจำ
                </h1>
                <p class="lead mb-0">
                    บันทึกช่วงเวลาสำคัญและความทรงจำอันมีค่า ที่เราได้ร่วมดูแลและให้บริการด้วยความเคารพ
                </p>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- Filter Section -->
    @if($categories->count() > 0)
    <div class="row mb-5">
        <div class="col-12">
            <div class="text-center">
                <h3 class="section-title">หมวดหมู่กิจกรรม</h3>
                <div class="filter-buttons mt-4">
                    <button class="btn filter-btn {{ !request('category') ? 'active' : '' }}" 
                            onclick="filterByCategory('')">
                        <i class="fas fa-th-large me-2"></i>ทั้งหมด
                    </button>
                    @foreach($categories as $category)
                        <button class="btn filter-btn {{ request('category') == $category->id ? 'active' : '' }}" 
                                onclick="filterByCategory('{{ $category->id }}')"
                                style="border-color: {{ $category->color }}; {{ request('category') == $category->id ? 'background-color: ' . $category->color . '; border-color: ' . $category->color . ';' : '' }}">
                            <i class="fas fa-tag me-2"></i>{{ $category->name }}
                        </button>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Activities Grid -->
    <div class="row">
        @forelse($activities as $activity)
            <div class="col-lg-4 col-md-6 col-12 mb-4">
                <div class="card memorial-card activity-card h-100">
                    <div class="position-relative overflow-hidden">
                        <img src="{{ $activity->cover_image_url }}" 
                             class="card-img-top activity-image" 
                             alt="{{ $activity->title }}"
                             onerror="this.src='{{ asset('images/no-image.svg') }}'">
                        
                        <!-- Category Badge -->
                        @if($activity->category)
                            <span class="category-badge" style="background-color: {{ $activity->category->color }};">
                                {{ $activity->category->name }}
                            </span>
                        @endif
                        
                        <!-- Image Count Badge -->
                        <span class="image-count-badge">
                            <i class="fas fa-images me-1"></i>{{ $activity->images->count() }} รูป
                        </span>
                    </div>
                    
                    <div class="card-body">
                        <h5 class="card-title fw-bold">{{ $activity->title }}</h5>
                        <p class="card-text text-muted">{{ Str::limit($activity->description, 100) }}</p>
                        
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                {{ $activity->activity_date ? $activity->activity_date->format('d/m/Y') : $activity->created_at->format('d/m/Y') }}
                            </small>
                            @if($activity->location)
                                <small class="text-muted">
                                    <i class="fas fa-map-marker-alt me-1"></i>{{ Str::limit($activity->location, 20) }}
                                </small>
                            @endif
                        </div>
                        
                        <div class="mt-3">
                            <a href="{{ route('activities.show', $activity) }}" class="btn btn-primary btn-sm w-100">
                                <i class="fas fa-eye me-2"></i>ดูแกลอรี่
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        @empty
            <div class="col-12">
                <div class="text-center py-5">
                    <div class="mb-4">
                        <i class="fas fa-camera fa-4x text-muted"></i>
                    </div>
                    <h4 class="text-muted">ยังไม่มีกิจกรรม</h4>
                    <p class="text-muted">กิจกรรมจะแสดงที่นี่เมื่อมีการเพิ่มข้อมูล</p>
                </div>
            </div>
        @endforelse
    </div>
    
    <!-- Load More Button (if needed) -->
    @if($activities->count() >= 12)
    <div class="row mt-5">
        <div class="col-12 text-center">
            <button class="btn btn-outline-primary btn-lg" id="loadMoreBtn">
                <i class="fas fa-plus me-2"></i>โหลดเพิ่มเติม
            </button>
        </div>
    </div>
    @endif
</div>
@endsection

@section('scripts')
<script>
function filterByCategory(categoryId) {
    const url = new URL(window.location);
    if (categoryId) {
        url.searchParams.set('category', categoryId);
    } else {
        url.searchParams.delete('category');
    }
    window.location.href = url.toString();
}

// Smooth scroll animation
document.addEventListener('DOMContentLoaded', function() {
    const cards = document.querySelectorAll('.activity-card');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                setTimeout(() => {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }, index * 100);
            }
        });
    });
    
    cards.forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(card);
    });
});
</script>
@endsection
