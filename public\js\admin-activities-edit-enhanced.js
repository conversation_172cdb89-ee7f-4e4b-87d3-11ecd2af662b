/**
 * Enhanced Activities Edit JavaScript
 * ฟังก์ชันสำหรับหน้าแก้ไขกิจกรรมที่มีแกลเลอรี่
 */

class ActivityEditManager {
    constructor() {
        this.csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        this.activityId = this.getActivityIdFromUrl();
        this.init();
    }

    init() {
        console.log('ActivityEditManager initialized');
        this.setupEventListeners();
        this.setupImagePreviews();
        this.setupFormValidation();
    }

    getActivityIdFromUrl() {
        const pathParts = window.location.pathname.split('/');
        const activitiesIndex = pathParts.indexOf('activities');
        return activitiesIndex !== -1 ? pathParts[activitiesIndex + 1] : null;
    }

    setupEventListeners() {
        // Cover image change
        const coverImageInput = document.getElementById('cover_image');
        if (coverImageInput) {
            coverImageInput.addEventListener('change', (e) => this.handleCoverImageChange(e));
        }

        // Gallery images change
        const galleryImagesInput = document.getElementById('gallery_images');
        if (galleryImagesInput) {
            galleryImagesInput.addEventListener('change', (e) => this.handleGalleryImagesChange(e));
        }

        // Form submission
        const form = document.getElementById('activityForm');
        if (form) {
            form.addEventListener('submit', (e) => this.handleFormSubmit(e));
        }

        // Drag and drop for gallery
        this.setupDragAndDrop();
    }

    setupImagePreviews() {
        // Setup existing gallery images with enhanced interactions
        const galleryItems = document.querySelectorAll('.gallery-item[data-image-id]');
        galleryItems.forEach(item => {
            this.enhanceGalleryItem(item);
        });
    }

    enhanceGalleryItem(item) {
        const img = item.querySelector('img');
        if (img) {
            // Add click to view full size
            img.style.cursor = 'pointer';
            img.addEventListener('click', () => {
                this.showImageModal(img.src);
            });

            // Add hover effects
            item.addEventListener('mouseenter', () => {
                item.style.transform = 'scale(1.05)';
                item.style.transition = 'transform 0.3s ease';
            });

            item.addEventListener('mouseleave', () => {
                item.style.transform = 'scale(1)';
            });
        }
    }

    handleCoverImageChange(event) {
        const file = event.target.files[0];
        if (!file) return;

        if (!this.validateImageFile(file)) {
            event.target.value = '';
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            const previewContainer = document.getElementById('coverPreview');
            if (previewContainer) {
                previewContainer.innerHTML = `
                    <div class="mt-2 fade-in">
                        <img src="${e.target.result}" class="current-image" alt="New Cover Preview">
                        <div class="mt-2">
                            <small class="text-success">
                                <i class="fas fa-check-circle me-1"></i>รูปภาพใหม่ที่เลือก
                            </small>
                        </div>
                    </div>
                `;
                
                // Reset remove flag
                const removeFlag = document.getElementById('removeCoverImageFlag');
                if (removeFlag) {
                    removeFlag.value = '0';
                }
            }
        };
        reader.readAsDataURL(file);

        this.showToast('เลือกรูปภาพหน้าปกใหม่แล้ว', 'success');
    }

    handleGalleryImagesChange(event) {
        const files = Array.from(event.target.files);
        if (files.length === 0) return;

        // Validate all files first
        const invalidFiles = files.filter(file => !this.validateImageFile(file));
        if (invalidFiles.length > 0) {
            this.showToast(`ไฟล์ ${invalidFiles.length} ไฟล์ไม่ถูกต้อง`, 'error');
            event.target.value = '';
            return;
        }

        const previewContainer = document.getElementById('newGalleryPreview');
        if (!previewContainer) return;

        previewContainer.innerHTML = '';

        files.forEach((file, index) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                const galleryItem = this.createGalleryPreviewItem(e.target.result, index, true);
                previewContainer.appendChild(galleryItem);
            };
            reader.readAsDataURL(file);
        });

        this.showToast(`เลือกรูปภาพ ${files.length} รูปสำหรับแกลเลอรี่`, 'success');
    }

    createGalleryPreviewItem(src, index, isNew = false) {
        const colDiv = document.createElement('div');
        colDiv.className = 'col-6 mb-2';

        const galleryItem = document.createElement('div');
        galleryItem.className = 'gallery-item fade-in';
        if (isNew) {
            galleryItem.setAttribute('data-new-index', index);
        }

        galleryItem.innerHTML = `
            <img src="${src}" alt="${isNew ? 'New Gallery Preview' : 'Gallery Image'}" style="cursor: pointer;">
            <button type="button" class="remove-btn" onclick="${isNew ? `removeNewGalleryItem(${index})` : ''}">
                <i class="fas fa-times"></i>
            </button>
        `;

        // Add click to view
        const img = galleryItem.querySelector('img');
        img.addEventListener('click', () => this.showImageModal(src));

        // Add hover effects
        this.enhanceGalleryItem(galleryItem);

        colDiv.appendChild(galleryItem);
        return colDiv;
    }

    validateImageFile(file) {
        const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        const maxSize = 2 * 1024 * 1024; // 2MB

        if (!validTypes.includes(file.type)) {
            this.showToast('กรุณาเลือกไฟล์รูปภาพที่ถูกต้อง (JPEG, PNG, GIF, WebP)', 'error');
            return false;
        }

        if (file.size > maxSize) {
            this.showToast('ขนาดไฟล์ต้องไม่เกิน 2MB', 'error');
            return false;
        }

        return true;
    }

    setupDragAndDrop() {
        const uploadAreas = document.querySelectorAll('.image-upload-area');
        
        uploadAreas.forEach(area => {
            area.addEventListener('dragover', (e) => {
                e.preventDefault();
                area.classList.add('dragover');
            });

            area.addEventListener('dragleave', (e) => {
                e.preventDefault();
                area.classList.remove('dragover');
            });

            area.addEventListener('drop', (e) => {
                e.preventDefault();
                area.classList.remove('dragover');
                
                const files = Array.from(e.dataTransfer.files);
                const imageFiles = files.filter(file => file.type.startsWith('image/'));
                
                if (imageFiles.length > 0) {
                    const galleryInput = document.getElementById('gallery_images');
                    if (galleryInput) {
                        const dt = new DataTransfer();
                        imageFiles.forEach(file => dt.items.add(file));
                        galleryInput.files = dt.files;
                        galleryInput.dispatchEvent(new Event('change'));
                    }
                }
            });
        });
    }

    setupFormValidation() {
        const form = document.getElementById('activityForm');
        if (!form) return;

        const requiredFields = form.querySelectorAll('input[required], textarea[required]');
        
        requiredFields.forEach(field => {
            field.addEventListener('blur', () => {
                this.validateField(field);
            });

            field.addEventListener('input', () => {
                if (field.classList.contains('is-invalid')) {
                    this.validateField(field);
                }
            });
        });
    }

    validateField(field) {
        const value = field.value.trim();
        const isValid = value !== '';
        
        field.classList.toggle('is-invalid', !isValid);
        field.classList.toggle('is-valid', isValid);
        
        return isValid;
    }

    handleFormSubmit(event) {
        const form = event.target;
        const submitBtn = form.querySelector('button[type="submit"]');
        
        // Validate form
        const requiredFields = form.querySelectorAll('input[required], textarea[required]');
        let isValid = true;
        
        requiredFields.forEach(field => {
            if (!this.validateField(field)) {
                isValid = false;
            }
        });

        if (!isValid) {
            event.preventDefault();
            this.showToast('กรุณากรอกข้อมูลให้ครบถ้วน', 'error');
            return;
        }

        // Show loading state
        if (submitBtn && window.AdminUnified) {
            AdminUnified.showButtonLoading(submitBtn, 'กำลังอัปเดต...');
        }
    }

    showImageModal(src) {
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                imageUrl: src,
                imageAlt: 'Gallery Image',
                showCloseButton: true,
                showConfirmButton: false,
                width: 'auto',
                padding: '1rem',
                background: '#fff',
                customClass: {
                    image: 'img-fluid'
                }
            });
        } else {
            // Fallback: open in new window
            window.open(src, '_blank');
        }
    }

    showToast(message, type = 'success') {
        if (window.AdminUnified) {
            AdminUnified.showToast(message, type);
        } else if (typeof toastr !== 'undefined') {
            toastr[type](message);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }

    // Public methods for global access
    deleteGalleryImage(imageId) {
        const confirmMessage = 'คุณต้องการลบรูปภาพนี้หรือไม่?';
        
        const performDelete = () => {
            fetch(`/admin/activities/${this.activityId}/images/${imageId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': this.csrfToken,
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const galleryItem = document.querySelector(`[data-image-id="${imageId}"]`);
                    if (galleryItem) {
                        const colElement = galleryItem.closest('.col-6');
                        if (colElement) {
                            colElement.style.transition = 'opacity 0.3s ease';
                            colElement.style.opacity = '0';
                            setTimeout(() => {
                                colElement.remove();
                                this.updateGalleryCount();
                            }, 300);
                        }
                    }
                    this.showToast('ลบรูปภาพสำเร็จ', 'success');
                } else {
                    this.showToast(data.message || 'เกิดข้อผิดพลาด', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                this.showToast('เกิดข้อผิดพลาดในการลบรูปภาพ', 'error');
            });
        };

        if (window.AdminUnified) {
            AdminUnified.showConfirmation(confirmMessage, performDelete);
        } else if (confirm(confirmMessage)) {
            performDelete();
        }
    }

    removeCoverImage() {
        const confirmMessage = 'คุณต้องการลบรูปภาพหน้าปกหรือไม่?';
        
        const performRemove = () => {
            const removeFlag = document.getElementById('removeCoverImageFlag');
            if (removeFlag) {
                removeFlag.value = '1';
            }
            
            const currentCoverImage = document.querySelector('.current-cover-image');
            if (currentCoverImage) {
                currentCoverImage.style.transition = 'opacity 0.3s ease';
                currentCoverImage.style.opacity = '0';
            }
            
            const previewContainer = document.getElementById('coverPreview');
            if (previewContainer) {
                previewContainer.innerHTML = `
                    <div class="alert alert-warning fade-in">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        รูปภาพจะถูกลบเมื่อบันทึกข้อมูล
                    </div>
                `;
            }
            
            this.showToast('รูปภาพหน้าปกจะถูกลบเมื่อบันทึกข้อมูล', 'warning');
        };

        if (window.AdminUnified) {
            AdminUnified.showConfirmation(confirmMessage, performRemove);
        } else if (confirm(confirmMessage)) {
            performRemove();
        }
    }

    removeNewGalleryItem(index) {
        const galleryItem = document.querySelector(`[data-new-index="${index}"]`);
        if (galleryItem) {
            const colElement = galleryItem.closest('.col-6');
            if (colElement) {
                colElement.remove();
            }
        }
        
        // Reset file input
        const input = document.getElementById('gallery_images');
        if (input) {
            const dt = new DataTransfer();
            const files = Array.from(input.files);
            
            files.forEach((file, i) => {
                if (i !== index) {
                    dt.items.add(file);
                }
            });
            
            input.files = dt.files;
        }
    }

    updateGalleryCount() {
        const currentCount = document.querySelectorAll('.gallery-item[data-image-id]').length;
        const headerTitle = document.querySelector('.card-header h6');
        if (headerTitle) {
            headerTitle.innerHTML = `<i class="fas fa-images me-2"></i>แกลเลอรี่ภาพ (${currentCount} รูป)`;
        }
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.activityEditManager = new ActivityEditManager();
});

// Global functions for onclick handlers
function deleteGalleryImage(imageId) {
    if (window.activityEditManager) {
        window.activityEditManager.deleteGalleryImage(imageId);
    }
}

function removeCoverImage() {
    if (window.activityEditManager) {
        window.activityEditManager.removeCoverImage();
    }
}

function removeNewGalleryItem(index) {
    if (window.activityEditManager) {
        window.activityEditManager.removeNewGalleryItem(index);
    }
}
