<?php
echo "<h1>Apache Test Page</h1>";
echo "<p>Current Time: " . date('Y-m-d H:i:s') . "</p>";
echo "<p>PHP Version: " . phpversion() . "</p>";
echo "<p>Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p>Script Name: " . $_SERVER['SCRIPT_NAME'] . "</p>";
echo "<p>Request URI: " . $_SERVER['REQUEST_URI'] . "</p>";

// Test mod_rewrite
if (function_exists('apache_get_modules')) {
    $modules = apache_get_modules();
    echo "<h2>Apache Modules:</h2>";
    echo "<p>mod_rewrite: " . (in_array('mod_rewrite', $modules) ? 'Enabled' : 'Disabled') . "</p>";
} else {
    echo "<p>Cannot check Apache modules (not running under Apache or function disabled)</p>";
}

// Test Laravel
echo "<h2>Laravel Test:</h2>";
if (file_exists('../bootstrap/app.php')) {
    echo "<p><PERSON><PERSON> bootstrap file exists</p>";
    try {
        require_once '../vendor/autoload.php';
        $app = require_once '../bootstrap/app.php';
        echo "<p>Laravel application loaded successfully</p>";
    } catch (Exception $e) {
        echo "<p>Laravel error: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p>Laravel bootstrap file not found</p>";
}
?>
