/* Memorial Theme - High-tech Black & White Design */

/* Advanced Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(201, 169, 110, 0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(201, 169, 110, 0.8);
    }
}

/* High-tech Button Effects */
.btn-memorial {
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #1a1a1a 0%, #000000 100%);
    border: 1px solid var(--memorial-gold);
    color: white;
    transition: all 0.3s ease;
}

.btn-memorial::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.5s;
}

.btn-memorial:hover::before {
    left: 100%;
}

.btn-memorial:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(201, 169, 110, 0.3);
    color: white;
}

/* Glowing Text Effect */
.glow-text {
    text-shadow: 0 0 10px rgba(201, 169, 110, 0.5);
    animation: glow 2s ease-in-out infinite alternate;
}

/* Particle Background Effect */
.particles-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
}

.particle {
    position: absolute;
    width: 2px;
    height: 2px;
    background: rgba(201, 169, 110, 0.3);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0) rotate(0deg);
        opacity: 0;
    }
    10%, 90% {
        opacity: 1;
    }
    50% {
        transform: translateY(-100px) rotate(180deg);
    }
}

/* Advanced Card Hover Effects */
.memorial-card {
    position: relative;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(201, 169, 110, 0.2);
    transition: all 0.4s ease;
    overflow: hidden;
}

.memorial-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent 0%, rgba(201, 169, 110, 0.05) 50%, transparent 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.memorial-card:hover::before {
    opacity: 1;
}

.memorial-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    border-color: var(--memorial-gold);
}

/* Elegant Divider */
.memorial-divider {
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--memorial-gold), transparent);
    margin: 2rem 0;
    position: relative;
}

.memorial-divider::before {
    content: '✦';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    color: var(--memorial-gold);
    padding: 0 15px;
    font-size: 1.2rem;
}

/* Scroll Progress Bar */
.scroll-progress {
    position: fixed;
    top: 0;
    left: 0;
    width: 0%;
    height: 3px;
    background: linear-gradient(90deg, var(--memorial-gold), var(--memorial-silver));
    z-index: 9999;
    transition: width 0.1s ease;
}

/* Memorial Quote Box */
.memorial-quote {
    background: linear-gradient(135deg, rgba(0,0,0,0.8) 0%, rgba(26,26,26,0.9) 100%);
    color: white;
    padding: 2rem;
    border-left: 4px solid var(--memorial-gold);
    border-radius: 0 15px 15px 0;
    position: relative;
    margin: 2rem 0;
}

.memorial-quote::before {
    content: '"';
    position: absolute;
    top: -10px;
    left: 20px;
    font-size: 4rem;
    color: var(--memorial-gold);
    opacity: 0.3;
}

/* High-tech Loading Spinner */
.memorial-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(201, 169, 110, 0.3);
    border-top: 3px solid var(--memorial-gold);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

/* Elegant Section Backgrounds */
.section-elegant {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 50%, #f8f9fa 100%);
    position: relative;
}

.section-elegant::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="0.5" fill="rgba(201,169,110,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    pointer-events: none;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .memorial-card:hover {
        transform: translateY(-5px) scale(1.01);
    }
    
    .memorial-quote {
        padding: 1.5rem;
        margin: 1rem 0;
    }
    
    .memorial-quote::before {
        font-size: 3rem;
        top: -5px;
        left: 15px;
    }
}

/* Print Styles */
@media print {
    .memorial-card,
    .btn-memorial {
        box-shadow: none !important;
        transform: none !important;
    }
    
    .particles-bg,
    .scroll-progress {
        display: none !important;
    }
}
