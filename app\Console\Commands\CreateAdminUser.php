<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class CreateAdminUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'admin:create';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create an admin user';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->ask('Enter admin email', '<EMAIL>');
        $password = $this->secret('Enter admin password');

        if (!$password) {
            $password = 'admin123';
            $this->info('Using default password: admin123');
        }

        // Check if user already exists
        $existingUser = User::where('email', $email)->first();

        if ($existingUser) {
            if ($this->confirm('User already exists. Update to admin?')) {
                $existingUser->update([
                    'is_admin' => true,
                    'password' => Hash::make($password)
                ]);
                $this->info('User updated to admin successfully!');
            }
        } else {
            User::create([
                'name' => 'Admin',
                'email' => $email,
                'password' => Hash::make($password),
                'is_admin' => true,
                'email_verified_at' => now()
            ]);
            $this->info('Admin user created successfully!');
        }

        $this->info("Email: {$email}");
        $this->info("Password: {$password}");
    }
}
