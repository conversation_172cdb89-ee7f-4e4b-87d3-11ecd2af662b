<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ActivityCategory;
use Illuminate\Http\Request;

class ActivityCategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $categories = ActivityCategory::withCount('activities')->orderBy('name')->get();
        return view('admin.activity-categories.index', compact('categories'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.activity-categories.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $data = $request->validate([
                'name' => 'required|string|max:255|unique:activity_categories,name',
                'description' => 'nullable|string',
                'color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
            ]);

            $data['is_active'] = $request->boolean('is_active');

            $category = ActivityCategory::create($data);

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'เพิ่มหมวดหมู่สำเร็จ',
                    'category' => $category->load('activities')
                ]);
            }

            return redirect()->route('admin.activity-categories.index')->with('success', 'เพิ่มหมวดหมู่สำเร็จ');
        } catch (\Exception $e) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()
                ], 500);
            }

            return back()->withInput()->withErrors(['error' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(ActivityCategory $activityCategory)
    {
        if (request()->ajax()) {
            return response()->json([
                'id' => $activityCategory->id,
                'name' => $activityCategory->name,
                'description' => $activityCategory->description,
                'color' => $activityCategory->color,
                'is_active' => $activityCategory->is_active,
                'activities_count' => $activityCategory->activities()->count()
            ]);
        }

        return view('admin.activity-categories.show', compact('activityCategory'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ActivityCategory $activityCategory)
    {
        return view('admin.activity-categories.edit', compact('activityCategory'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ActivityCategory $activityCategory)
    {
        try {
            $data = $request->validate([
                'name' => 'required|string|max:255|unique:activity_categories,name,' . $activityCategory->id,
                'description' => 'nullable|string',
                'color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
            ]);

            $data['is_active'] = $request->boolean('is_active');

            $activityCategory->update($data);

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'อัปเดตหมวดหมู่สำเร็จ',
                    'category' => $activityCategory->fresh()->load('activities')
                ]);
            }

            return redirect()->route('admin.activity-categories.index')->with('success', 'อัปเดตหมวดหมู่สำเร็จ');
        } catch (\Exception $e) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()
                ], 500);
            }

            return back()->withInput()->withErrors(['error' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()]);
        }
    }

    /**
     * Toggle category status
     */
    public function toggleStatus(Request $request, ActivityCategory $activityCategory)
    {
        try {
            $activityCategory->update([
                'is_active' => $request->is_active
            ]);

            return response()->json([
                'success' => true,
                'message' => $request->is_active ? 'เปิดใช้งานหมวดหมู่แล้ว' : 'ปิดใช้งานหมวดหมู่แล้ว'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'เกิดข้อผิดพลาดในการเปลี่ยนสถานะ'
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ActivityCategory $activityCategory)
    {
        try {
            // Check if category has activities
            if ($activityCategory->activities()->count() > 0) {
                if (request()->ajax()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'ไม่สามารถลบหมวดหมู่ที่มีกิจกรรมอยู่ได้'
                    ], 400);
                }

                return redirect()->route('admin.activity-categories.index')
                    ->withErrors(['error' => 'ไม่สามารถลบหมวดหมู่ที่มีกิจกรรมอยู่ได้']);
            }

            $activityCategory->delete();

            if (request()->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'ลบหมวดหมู่สำเร็จ'
                ]);
            }

            return redirect()->route('admin.activity-categories.index')->with('success', 'ลบหมวดหมู่สำเร็จ');
        } catch (\Exception $e) {
            if (request()->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'เกิดข้อผิดพลาดในการลบหมวดหมู่'
                ], 500);
            }

            return back()->withErrors(['error' => 'เกิดข้อผิดพลาดในการลบหมวดหมู่']);
        }
    }

    /**
     * Bulk delete categories
     */
    public function bulkDelete(Request $request)
    {
        try {
            $categoryIds = $request->category_ids;
            
            if (empty($categoryIds)) {
                return response()->json([
                    'success' => false,
                    'message' => 'กรุณาเลือกหมวดหมู่ที่ต้องการลบ'
                ], 400);
            }

            $categories = ActivityCategory::whereIn('id', $categoryIds)->get();
            $deletedCount = 0;
            $skippedCount = 0;
            
            foreach ($categories as $category) {
                if ($category->activities()->count() > 0) {
                    $skippedCount++;
                    continue;
                }
                
                $category->delete();
                $deletedCount++;
            }

            $message = "ลบหมวดหมู่สำเร็จ {$deletedCount} รายการ";
            if ($skippedCount > 0) {
                $message .= " (ข้าม {$skippedCount} รายการที่มีกิจกรรมอยู่)";
            }

            return response()->json([
                'success' => true,
                'message' => $message
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'เกิดข้อผิดพลาดในการลบหมวดหมู่'
            ], 500);
        }
    }
}
