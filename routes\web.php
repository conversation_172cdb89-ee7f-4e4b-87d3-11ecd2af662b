<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomepageContentController;
use App\Http\Controllers\ServiceController;
use App\Http\Controllers\PackageController;
use App\Http\Controllers\ActivityController;
use App\Http\Controllers\ContactController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// ===================== หน้าบ้าน (Public) =====================
Route::get('/', function () {
    $contents = \App\Models\HomepageContent::all();
    $siteSettings = \App\Models\SiteSetting::getSettings();
    return view('home', compact('contents', 'siteSettings'));
})->name('home');
Route::get('/services', [ServiceController::class, 'index'])->name('services.index');
Route::get('/services/{service}', [ServiceController::class, 'show'])->name('services.show');
Route::get('/packages', [PackageController::class, 'index'])->name('packages.index');
Route::get('/packages/{package}', [PackageController::class, 'show'])->name('packages.show');
Route::get('/activities', [ActivityController::class, 'index'])->name('activities.index');
Route::get('/activities/{activity}', [ActivityController::class, 'show'])->name('activities.show');
Route::get('/contact', [ContactController::class, 'index'])->name('contact.index');
Route::post('/contact', [ContactController::class, 'store'])->name('contact.store');

// ===================== หลังบ้าน (Admin) =====================
Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/', [App\Http\Controllers\Admin\DashboardController::class, 'index'])->name('dashboard');

    // Services Routes
    Route::resource('services', App\Http\Controllers\Admin\ServiceController::class);
    Route::post('services/bulk-delete', [App\Http\Controllers\Admin\ServiceController::class, 'bulkDelete'])->name('services.bulk-delete');

    // Packages Routes
    Route::resource('packages', App\Http\Controllers\Admin\PackageController::class);
    Route::post('packages/bulk-delete', [App\Http\Controllers\Admin\PackageController::class, 'bulkDelete'])->name('packages.bulk-delete');

    // Activities Routes
    Route::resource('activities', App\Http\Controllers\Admin\ActivityController::class);
    Route::post('activities/{activity}/toggle-publish', [App\Http\Controllers\Admin\ActivityController::class, 'togglePublish'])->name('activities.toggle-publish');
    Route::post('activities/bulk-delete', [App\Http\Controllers\Admin\ActivityController::class, 'bulkDelete'])->name('activities.bulk-delete');

    // Activity images management
    Route::delete('activities/{activity}/images/{image}', [App\Http\Controllers\Admin\ActivityController::class, 'deleteImage'])->name('activities.images.delete');
    Route::put('activities/images/{image}/caption', [App\Http\Controllers\Admin\ActivityController::class, 'updateImageCaption'])->name('activities.images.update-caption');
    Route::put('activities/{activity}/images/order', [App\Http\Controllers\Admin\ActivityController::class, 'updateImageOrder'])->name('activities.images.update-order');

    // Activity Categories Routes
    Route::resource('activity-categories', App\Http\Controllers\Admin\ActivityCategoryController::class);
    Route::post('activity-categories/{activityCategory}/toggle-status', [App\Http\Controllers\Admin\ActivityCategoryController::class, 'toggleStatus'])->name('activity-categories.toggle-status');
    Route::post('activity-categories/bulk-delete', [App\Http\Controllers\Admin\ActivityCategoryController::class, 'bulkDelete'])->name('activity-categories.bulk-delete');

    // Homepage Routes
    Route::resource('homepage', HomepageContentController::class)->only(['index','edit','update'])->parameters(['homepage' => 'homepageContent']);

    // Contacts Routes
    Route::resource('contacts', ContactController::class)->only(['index','edit','update','destroy']);

    // Site Settings Routes without file upload
    Route::get('settings', [App\Http\Controllers\SiteSettingController::class, 'index'])->name('settings.index');
    Route::delete('settings/remove-logo', [App\Http\Controllers\SiteSettingController::class, 'removeLogo'])->name('settings.remove-logo');
    Route::delete('settings/remove-favicon', [App\Http\Controllers\SiteSettingController::class, 'removeFavicon'])->name('settings.remove-favicon');
    Route::delete('settings/remove-hero-icon', [App\Http\Controllers\SiteSettingController::class, 'removeHeroIcon'])->name('settings.remove-hero-icon');
});

// Test Routes
require __DIR__.'/test.php';

// Authentication Routes (ปิดการลงทะเบียนสาธารณะ)
Auth::routes(['register' => false]);

// Home route - redirect to admin for authenticated users
Route::get('/home', function () {
    if (auth()->check() && auth()->user()->is_admin) {
        return redirect()->route('admin.dashboard');
    }
    return redirect('/');
})->middleware('auth')->name('home.index');
