<?php

use Illuminate\Support\Facades\Route;
use App\Models\Activity;
use App\Models\ActivityCategory;

Route::get('/test-activities', function () {
    try {
        $activities = Activity::with(['category', 'images'])->latest()->get();
        $categories = ActivityCategory::active()->get();
        
        return response()->json([
            'success' => true,
            'activities_count' => $activities->count(),
            'categories_count' => $categories->count(),
            'activities' => $activities->take(3)->map(function($activity) {
                return [
                    'id' => $activity->id,
                    'title' => $activity->title,
                    'category' => $activity->category ? $activity->category->name : null,
                    'images_count' => $activity->images->count()
                ];
            })
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ], 500);
    }
});
