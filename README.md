# 🛍️ SoloShop - ระบบจัดการเว็บไซต์ธุรกิจ

ระบบจัดการเว็บไซต์ธุรกิจที่พัฒนาด้วย Laravel สำหรับการจัดการบริการ แพ็กเกจ และกิจกรรมต่างๆ

## 🚀 การติดตั้ง

```bash
# 1. Clone repository
git clone [repository-url]
cd SoloShop

# 2. ติดตั้ง dependencies
composer install
npm install

# 3. ตั้งค่า environment
cp .env.example .env
php artisan key:generate

# 4. ตั้งค่าฐานข้อมูล (แก้ไขใน .env)
# DB_DATABASE=soloshop
# DB_USERNAME=root
# DB_PASSWORD=

# 5. รัน migrations และ seeders
php artisan migrate
php artisan db:seed

# 6. สร้าง storage link
php artisan storage:link

# 7. Compile assets
npm run dev

# 8. เริ่มเซิร์ฟเวอร์
php artisan serve
```

## ✨ ฟีเจอร์หลัก

### 🏠 **หน้าบ้าน (Frontend)**
- แสดงบริการและแพ็กเกจ
- แกลเลอรี่กิจกรรม
- ฟอร์มติดต่อ
- Responsive design

### 🔧 **ระบบหลังบ้าน (Admin Panel)**
- **Dashboard** - สถิติและภาพรวมระบบ
- **จัดการบริการ** - เพิ่ม/แก้ไข/ลบบริการ
- **จัดการแพ็กเกจ** - จัดการแพ็กเกจสินค้า
- **จัดการกิจกรรม** - อัปโหลดรูปภาพและจัดการแกลเลอรี่
- **จัดการหมวดหมู่** - จัดหมวดหมู่กิจกรรม
- **ข้อความติดต่อ** - ดูและจัดการข้อความจากลูกค้า
- **ตั้งค่าเว็บไซต์** - แก้ไขข้อมูลเว็บไซต์
- **จัดการหน้าแรก** - แก้ไขเนื้อหาหน้าแรก

### 🛡️ **ระบบความปลอดภัย**
- Authentication & Authorization
- Admin middleware protection
- File upload validation
- CSRF protection
- Input sanitization

### 📱 **การจัดการไฟล์**
- Image upload with validation
- Automatic image resizing
- Multiple image gallery support
- File type and size validation

## 🔑 **การเข้าสู่ระบบ Admin**

```
URL: http://localhost:8000/login
Email: <EMAIL>
Password: password
```

## 📁 **โครงสร้างโปรเจค**

```
SoloShop/
├── app/
│   ├── Http/Controllers/     # Controllers
│   ├── Models/              # Eloquent Models
│   ├── Middleware/          # Custom Middleware
│   └── Helpers/             # Helper Classes
├── resources/
│   ├── views/               # Blade Templates
│   ├── css/                 # Stylesheets
│   └── js/                  # JavaScript
├── database/
│   ├── migrations/          # Database Migrations
│   └── seeders/             # Database Seeders
└── public/
    └── storage/             # Public Storage Link
```

## 🛠️ **เทคโนโลยีที่ใช้**

- **Backend**: Laravel 9.x
- **Frontend**: Bootstrap 5, jQuery
- **Database**: MySQL
- **Image Processing**: Intervention Image
- **Icons**: Font Awesome
- **Admin Theme**: AdminLTE

## 📋 **ข้อกำหนดระบบ**

- PHP >= 8.0
- MySQL >= 5.7
- Composer
- Node.js & NPM
- GD หรือ Imagick extension

## 🔧 **การพัฒนา**

```bash
# Development mode
npm run dev

# Watch for changes
npm run watch

# Production build
npm run production

# Clear cache
php artisan cache:clear
php artisan config:clear
php artisan view:clear
```

## 📞 **การสนับสนุน**

หากพบปัญหาหรือต้องการความช่วยเหลือ กรุณาติดต่อทีมพัฒนา

---

**SoloShop** - ระบบจัดการเว็บไซต์ธุรกิจที่ใช้งานง่าย สะดวก และมีประสิทธิภาพ
