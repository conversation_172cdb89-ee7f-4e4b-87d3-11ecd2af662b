# เพิ่มใน C:\xampp\apache\conf\extra\httpd-vhosts.conf

<VirtualHost *:80>
    DocumentRoot "D:/XAMPP/htdocs/SoloShop/public"
    ServerName soloshop.local
    ServerAlias www.soloshop.local
    <Directory "D:/XAMPP/htdocs/SoloShop/public">
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted
        DirectoryIndex index.php
    </Directory>
    ErrorLog "logs/soloshop_error.log"
    CustomLog "logs/soloshop_access.log" common
</VirtualHost>

# สำหรับการเข้าถึงผ่าน localhost/SoloShop/public
<VirtualHost *:80>
    DocumentRoot "D:/XAMPP/htdocs"
    ServerName localhost
    <Directory "D:/XAMPP/htdocs">
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted
        DirectoryIndex index.php index.html
    </Directory>
</VirtualHost>

# เพิ่มใน C:\Windows\System32\drivers\etc\hosts
127.0.0.1 soloshop.local
127.0.0.1 www.soloshop.local
